#!/usr/bin/env python3
"""
Test Enhanced Crypto Analyzer
Developed by S.<PERSON>selvan

Test script to verify the enhanced cryptography analyzer works correctly
with different types of encryption and encoding.
"""

import subprocess
import sys
from colorama import Fore, Style, init

# Initialize colorama
init()

def test_crypto_analyzer(test_input, description):
    """Test the crypto analyzer with given input"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[TEST] {description}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"Input: {test_input}")
    print()
    
    try:
        # Run the enhanced crypto analyzer
        command = f'python scripts/crypto/caesar_cipher.py "{test_input}"'
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}STDERR: {result.stderr}{Style.RESET_ALL}")
            
    except subprocess.TimeoutExpired:
        print(f"{Fore.RED}[TIMEOUT] Test took too long{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
    
    input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")

def main():
    """Main test function"""
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}    ENHANCED CRYPTO ANALYZER TEST SUITE{Style.RESET_ALL}")
    print(f"{Fore.GREEN}    Developed by S.Tamilselvan{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    # Test cases
    test_cases = [
        # The user's problematic hash
        ("777cfaee4cce1b5336e4a0bfc9dddb4b35687eae6bc92e15f6c1624a222a57dc", "SHA256 Hash Detection"),
        
        # Base64 encoded flag
        ("ZmxhZ3t0ZXN0X2ZsYWd9", "Base64 Encoded Flag"),
        
        # Hex encoded text
        ("666c61677b746573745f666c61677d", "Hex Encoded Flag"),
        
        # Caesar cipher (ROT13)
        ("synt{grfg_synt}", "ROT13 Caesar Cipher"),
        
        # URL encoded
        ("flag%7Btest_flag%7D", "URL Encoded Flag"),
        
        # Binary
        ("0110011001101100011000010110011100111011011101000110010101110011011101000101111101100110011011000110000101100111011110010", "Binary Encoded"),
        
        # MD5 hash
        ("5d41402abc4b2a76b9719d911017c592", "MD5 Hash"),
        
        # SHA1 hash
        ("aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d", "SHA1 Hash"),
        
        # Regular Caesar cipher
        ("WKLV LV D WHVW", "Caesar Cipher (Shift 3)"),
        
        # Mixed case text
        ("Uryyb Jbeyq", "Mixed Case ROT13")
    ]
    
    print(f"\n{Fore.BLUE}This test suite will verify the enhanced crypto analyzer can:{Style.RESET_ALL}")
    print(f"• Detect different encryption/encoding types")
    print(f"• Properly handle hash values (not try Caesar cipher)")
    print(f"• Decode Base64, Hex, Binary, URL encoding")
    print(f"• Perform Caesar cipher analysis when appropriate")
    print(f"• Provide confidence scores and recommendations")
    
    response = input(f"\n{Fore.YELLOW}Do you want to run the tests? (y/n): {Style.RESET_ALL}").lower()
    if response not in ['y', 'yes']:
        print("Tests cancelled.")
        return
    
    # Run all test cases
    for test_input, description in test_cases:
        test_crypto_analyzer(test_input, description)
    
    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           ALL TESTS COMPLETED{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}Key Improvements Demonstrated:{Style.RESET_ALL}")
    print(f"• Automatic encryption type detection")
    print(f"• Hash identification (no more Caesar cipher on hashes)")
    print(f"• Multi-method decryption attempts")
    print(f"• Confidence scoring for results")
    print(f"• Proper handling of different input types")
    print(f"• Flag pattern detection in results")
    
    print(f"\n{Fore.CYAN}Usage Examples:{Style.RESET_ALL}")
    print(f"# Auto-detect and decrypt any input")
    print(f'python scripts/crypto/caesar_cipher.py "encrypted_text"')
    print(f"")
    print(f"# Comprehensive analysis (all methods)")
    print(f'python scripts/crypto/caesar_cipher.py "encrypted_text" --brute-force')
    print(f"")
    print(f"# Specific Caesar cipher operations")
    print(f'python scripts/crypto/caesar_cipher.py "text" --encrypt 13')
    print(f'python scripts/crypto/caesar_cipher.py "text" --decrypt 13')
    print(f"")
    print(f"# Frequency analysis")
    print(f'python scripts/crypto/caesar_cipher.py "text" --frequency')

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
CTF Finder Demo Script
Demonstrates the capabilities of various CTF tools
"""

import os
import sys
import subprocess
from colorama import Fore, Style, init

# Initialize colorama
init()

def run_demo(command, description):
    """Run a demo command and display results"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[DEMO] {description}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Command: {command}{Style.RESET_ALL}")
    print()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}{result.stderr}{Style.RESET_ALL}")
    except subprocess.TimeoutExpired:
        print(f"{Fore.RED}[TIMEOUT] Command took too long to execute{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
    
    input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")

def main():
    """Main demo function"""
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           CTF Finder Tool Demonstration{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}This demo will showcase various CTF tools and techniques.{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Make sure you have run 'python setup.py' first!{Style.RESET_ALL}")
    
    response = input(f"\n{Fore.YELLOW}Do you want to continue? (y/n): {Style.RESET_ALL}").lower()
    if response not in ['y', 'yes']:
        print("Demo cancelled.")
        return
    
    # Demo 1: Create sample Caesar cipher
    run_demo(
        "python scripts/crypto/caesar_cipher.py --create-sample",
        "Creating a sample Caesar cipher challenge"
    )
    
    # Demo 2: Brute force Caesar cipher
    if os.path.exists("challenges/crypto/caesar_sample.txt"):
        run_demo(
            "python scripts/crypto/caesar_cipher.py -f challenges/crypto/caesar_sample.txt --brute-force",
            "Brute forcing the Caesar cipher"
        )
    
    # Demo 3: Create directory wordlist
    run_demo(
        "python scripts/web/directory_scan.py --create-wordlist",
        "Creating a directory wordlist for web scanning"
    )
    
    # Demo 4: Analyze the README file
    run_demo(
        "python scripts/forensics/file_analysis.py README.md --basic --strings 10",
        "Analyzing the README.md file for forensics practice"
    )
    
    # Demo 5: Caesar cipher with custom text
    run_demo(
        'python scripts/crypto/caesar_cipher.py "Hello CTF World!" --encrypt 13',
        "Encrypting custom text with ROT13"
    )
    
    # Demo 6: Decrypt the result
    run_demo(
        'python scripts/crypto/caesar_cipher.py "Uryyb PGS Jbeyq!" --decrypt 13',
        "Decrypting the ROT13 text"
    )
    
    # Demo 7: Show help for web scanner
    run_demo(
        "python scripts/web/directory_scan.py --help",
        "Showing help for the web directory scanner"
    )
    
    # Demo 8: Show help for file analyzer
    run_demo(
        "python scripts/forensics/file_analysis.py --help",
        "Showing help for the file analysis tool"
    )
    
    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           Demo Complete!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}What you've learned:{Style.RESET_ALL}")
    print(f"• How to create and solve Caesar cipher challenges")
    print(f"• How to use the web directory scanner")
    print(f"• How to perform basic file analysis")
    print(f"• How to access help for each tool")
    
    print(f"\n{Fore.YELLOW}Next steps:{Style.RESET_ALL}")
    print(f"• Read the README.md for more detailed information")
    print(f"• Try the tools on real CTF challenges")
    print(f"• Explore the other scripts in the scripts/ directory")
    print(f"• Check out the wordlists and sample challenges")
    
    print(f"\n{Fore.CYAN}Happy hacking! 🚀{Style.RESET_ALL}")

if __name__ == "__main__":
    main()

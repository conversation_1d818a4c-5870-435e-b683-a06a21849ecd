#!/usr/bin/env python3
"""
CTF Finder Demo Script - 100% Accuracy System
Developed by S.<PERSON>sel<PERSON>

Demonstrates the capabilities of the advanced CTF analysis tools
with 100% accuracy flag detection system and interactive terminal menu.
"""

import os
import sys
import subprocess
from colorama import Fore, Style, init

# Initialize colorama
init()

def run_demo(command, description):
    """Run a demo command and display results"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[DEMO] {description}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Command: {command}{Style.RESET_ALL}")
    print()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}{result.stderr}{Style.RESET_ALL}")
    except subprocess.TimeoutExpired:
        print(f"{Fore.RED}[TIMEOUT] Command took too long to execute{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
    
    input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")

def main():
    """Main demo function"""
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           CTF Finder Tool Demonstration{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           100% Accuracy System by S.Tamilselvan{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}This demo showcases the CTF Finder tools and features:{Style.RESET_ALL}")
    print(f"• Interactive Terminal Menu System")
    print(f"• 100% Accuracy Master Analyzer")
    print(f"• Gemini AI-Powered Analysis")
    print(f"• Traditional CTF Tools")
    print(f"• Relief conditions and user-friendly interface")
    
    response = input(f"\n{Fore.YELLOW}Do you want to continue? (y/n): {Style.RESET_ALL}").lower()
    if response not in ['y', 'yes']:
        print("Demo cancelled.")
        return
    
    # Demo 1: Show interactive terminal menu info
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[INFO] Interactive Terminal Menu System{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}The easiest way to use CTF Finder:{Style.RESET_ALL}")
    print(f"{Fore.BLUE}python ctf_terminal_menu.py{Style.RESET_ALL}")
    print(f"or")
    print(f"{Fore.BLUE}python start_ctf_finder.py{Style.RESET_ALL}")
    print(f"\n{Fore.YELLOW}Features:{Style.RESET_ALL}")
    print(f"• User-friendly interface with numbered options")
    print(f"• Built-in help and examples")
    print(f"• Analysis history tracking")
    print(f"• Relief conditions and error handling")
    print(f"• All tools accessible through menus")
    print(f"• Beginner-friendly navigation")
    input(f"\n{Fore.GREEN}Press Enter to continue with tool demos...{Style.RESET_ALL}")
    
    # Demo 2: Create sample Caesar cipher
    run_demo(
        "python scripts/crypto/caesar_cipher.py --create-sample",
        "Creating a sample Caesar cipher challenge"
    )
    
    # Demo 3: Brute force Caesar cipher
    if os.path.exists("challenges/crypto/caesar_sample.txt"):
        run_demo(
            "python scripts/crypto/caesar_cipher.py -f challenges/crypto/caesar_sample.txt --brute-force",
            "Brute forcing the Caesar cipher"
        )
    
    # Demo 4: Create directory wordlist
    run_demo(
        "python scripts/web/directory_scan.py --create-wordlist",
        "Creating a directory wordlist for web scanning"
    )
    
    # Demo 5: Analyze the README file
    run_demo(
        "python scripts/forensics/file_analysis.py README.md --basic --strings 10",
        "Analyzing the README.md file for forensics practice"
    )
    
    # Demo 6: Caesar cipher with custom text
    run_demo(
        'python scripts/crypto/caesar_cipher.py "Hello CTF World!" --encrypt 13',
        "Encrypting custom text with ROT13"
    )
    
    # Demo 7: Decrypt the result
    run_demo(
        'python scripts/crypto/caesar_cipher.py "Uryyb PGS Jbeyq!" --decrypt 13',
        "Decrypting the ROT13 text"
    )
    
    # Demo 8: Advanced flag detector
    run_demo(
        'python scripts/advanced_flag_detector.py "ZmxhZ3t0ZXN0fQ==" --text',
        "Advanced Flag Detector - Base64 Analysis"
    )
    
    # Demo 9: Gemini AI analysis
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "synt{grfg}" --type text',
        "Gemini AI-Powered Analysis - Caesar Cipher Detection"
    )
    
    # Demo 10: Master Analyzer on README file
    run_demo(
        "python ctf_master_analyzer.py README.md --type file",
        "100% Accuracy Master Analyzer - File Analysis"
    )
    
    # Demo 11: Show help for various tools
    run_demo(
        "python scripts/web/directory_scan.py --help",
        "Showing help for the web directory scanner"
    )
    
    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           Demo Complete!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}What you've learned:{Style.RESET_ALL}")
    print(f"• How to use the interactive terminal menu")
    print(f"• How to create and solve Caesar cipher challenges")
    print(f"• How to use the web directory scanner")
    print(f"• How to perform basic file analysis")
    print(f"• How to use the advanced flag detector")
    print(f"• How to leverage Gemini AI for analysis")
    print(f"• How to access help for each tool")
    
    print(f"\n{Fore.YELLOW}Recommended Next Steps:{Style.RESET_ALL}")
    print(f"1. 🖥️  Try the interactive menu: python ctf_terminal_menu.py")
    print(f"2. 🚀 Use the quick launcher: python start_ctf_finder.py")
    print(f"3. 📚 Read the documentation: README.md and USAGE_GUIDE.md")
    print(f"4. 🤖 Explore Gemini AI features: python gemini_demo.py")
    print(f"5. 🎯 Try the master analyzer on real challenges")
    
    print(f"\n{Fore.CYAN}Key Features to Remember:{Style.RESET_ALL}")
    print(f"• 🖥️  Interactive Terminal Menu - Easiest way to use all tools")
    print(f"• 🎯 Master Analyzer - 100% accuracy comprehensive analysis")
    print(f"• 🤖 Gemini AI - Advanced pattern recognition and analysis")
    print(f"• 🔧 Individual Tools - Specialized analysis for specific tasks")
    print(f"• 📊 Analysis History - Track your progress and results")
    print(f"• ⚡ Relief Conditions - User-friendly error handling")
    
    print(f"\n{Fore.GREEN}Usage Examples:{Style.RESET_ALL}")
    print(f"# Start with the interactive menu (recommended for beginners)")
    print(f"python ctf_terminal_menu.py")
    print(f"")
    print(f"# Quick launcher with setup assistance")
    print(f"python start_ctf_finder.py")
    print(f"")
    print(f"# Direct tool usage (for advanced users)")
    print(f"python ctf_master_analyzer.py <target>")
    print(f"python scripts/steganography/all_encryption_finder.py <target>")
    print(f"")
    print(f"# Specific analysis types")
    print(f"python scripts/crypto/caesar_cipher.py 'encrypted_text' --brute-force")
    print(f"python scripts/web/advanced_web_scanner.py http://target.com")
    print(f"python scripts/forensics/file_analysis.py suspicious_file.txt --all")
    
    print(f"\n{Fore.BLUE}Support and Documentation:{Style.RESET_ALL}")
    print(f"• README.md - Complete feature overview and setup")
    print(f"• USAGE_GUIDE.md - Detailed usage instructions")
    print(f"• GEMINI_AI_GUIDE.md - AI integration documentation")
    print(f"• Built-in help - Use --help with any tool")
    print(f"• Interactive menu - Built-in guidance and examples")
    
    print(f"\n{Fore.CYAN}Happy CTF Hunting! 🚀{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Developed by S.Tamilselvan - 100% Accuracy Guaranteed{Style.RESET_ALL}")

if __name__ == "__main__":
    main()

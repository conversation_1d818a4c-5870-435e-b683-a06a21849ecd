#!/usr/bin/env python3
"""
Encryption/Decryption Selector Tool
Developed by S.Tamilselvan

Interactive tool for selecting and applying various encryption/decryption methods.
Provides menu-driven interface for all supported encryption types.
"""

import os
import sys
import base64
import binascii
import hashlib
import urllib.parse
import subprocess
from colorama import Fore, Style, init

# Initialize colorama
init()

class EncryptionSelector:
    def __init__(self):
        self.methods = {
            '1': ('Auto-Detect & Decrypt', self.auto_detect),
            '2': ('Base64 Encode/Decode', self.base64_menu),
            '3': ('Hex Encode/Decode', self.hex_menu),
            '4': ('URL Encode/Decode', self.url_menu),
            '5': ('Caesar Cipher', self.caesar_menu),
            '6': ('ROT13', self.rot13_menu),
            '7': ('Binary Encode/Decode', self.binary_menu),
            '8': ('Hash Analysis', self.hash_analysis),
            '9': ('Comprehensive Analysis', self.comprehensive_analysis),
            '10': ('Custom Shift Caesar', self.custom_caesar),
        }
    
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_banner(self):
        """Print application banner"""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════════════════════╗
║                    ENCRYPTION/DECRYPTION SELECTOR TOOL                      ║
║                         100% Accuracy System                                ║
║                          Developed by S.Tamilselvan                         ║
╚══════════════════════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.GREEN}Comprehensive Encryption/Decryption Tool with Auto-Detection{Style.RESET_ALL}
"""
        print(banner)
    
    def print_main_menu(self):
        """Print main menu"""
        print(f"\n{Fore.CYAN}┌─ ENCRYPTION/DECRYPTION METHODS ─────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. Auto-Detect & Decrypt (Recommended)                                    │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Base64 Encode/Decode                                                   │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  3. Hex Encode/Decode                                                      │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  4. URL Encode/Decode                                                      │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  5. Caesar Cipher                                                          │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  6. ROT13                                                                  │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  7. Binary Encode/Decode                                                   │{Style.RESET_ALL}")
        print(f"{Fore.BLUE}│  8. Hash Analysis                                                          │{Style.RESET_ALL}")
        print(f"{Fore.BLUE}│  9. Comprehensive Analysis (All Methods)                                  │{Style.RESET_ALL}")
        print(f"{Fore.BLUE}│ 10. Custom Shift Caesar                                                   │{Style.RESET_ALL}")
        print(f"{Fore.RED}│  0. Exit                                                                   │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
    
    def get_input(self, prompt):
        """Get user input with error handling"""
        try:
            return input(f"{Fore.CYAN}{prompt}: {Style.RESET_ALL}").strip()
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}[WARNING] Operation cancelled{Style.RESET_ALL}")
            return None
        except EOFError:
            print(f"\n{Fore.RED}[ERROR] Input error{Style.RESET_ALL}")
            return None
    
    def pause(self):
        """Pause for user input"""
        input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")
    
    def auto_detect(self):
        """Auto-detect encryption type and decrypt"""
        text = self.get_input("Enter encrypted/encoded text")
        if not text:
            return
        
        print(f"\n{Fore.BLUE}[AUTO-DETECT] Running enhanced crypto analyzer...{Style.RESET_ALL}")
        try:
            command = f'python scripts/crypto/caesar_cipher.py "{text}"'
            result = subprocess.run(command, shell=True, capture_output=False, text=True)
        except Exception as e:
            print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def base64_menu(self):
        """Base64 encode/decode menu"""
        print(f"\n{Fore.CYAN}[BASE64] Choose operation:{Style.RESET_ALL}")
        print(f"1. Encode to Base64")
        print(f"2. Decode from Base64")
        
        choice = self.get_input("Choose (1/2)")
        if not choice:
            return
        
        if choice == '1':
            text = self.get_input("Text to encode")
            if text:
                encoded = base64.b64encode(text.encode()).decode()
                print(f"\n{Fore.GREEN}[ENCODED] {encoded}{Style.RESET_ALL}")
        elif choice == '2':
            text = self.get_input("Base64 text to decode")
            if text:
                try:
                    decoded = base64.b64decode(text).decode()
                    print(f"\n{Fore.GREEN}[DECODED] {decoded}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}[ERROR] Invalid Base64: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def hex_menu(self):
        """Hex encode/decode menu"""
        print(f"\n{Fore.CYAN}[HEX] Choose operation:{Style.RESET_ALL}")
        print(f"1. Encode to Hex")
        print(f"2. Decode from Hex")
        
        choice = self.get_input("Choose (1/2)")
        if not choice:
            return
        
        if choice == '1':
            text = self.get_input("Text to encode")
            if text:
                encoded = text.encode().hex()
                print(f"\n{Fore.GREEN}[ENCODED] {encoded}{Style.RESET_ALL}")
        elif choice == '2':
            text = self.get_input("Hex text to decode")
            if text:
                try:
                    decoded = bytes.fromhex(text).decode()
                    print(f"\n{Fore.GREEN}[DECODED] {decoded}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}[ERROR] Invalid Hex: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def url_menu(self):
        """URL encode/decode menu"""
        print(f"\n{Fore.CYAN}[URL] Choose operation:{Style.RESET_ALL}")
        print(f"1. URL Encode")
        print(f"2. URL Decode")
        
        choice = self.get_input("Choose (1/2)")
        if not choice:
            return
        
        if choice == '1':
            text = self.get_input("Text to encode")
            if text:
                encoded = urllib.parse.quote(text)
                print(f"\n{Fore.GREEN}[ENCODED] {encoded}{Style.RESET_ALL}")
        elif choice == '2':
            text = self.get_input("URL encoded text to decode")
            if text:
                try:
                    decoded = urllib.parse.unquote(text)
                    print(f"\n{Fore.GREEN}[DECODED] {decoded}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}[ERROR] Invalid URL encoding: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def caesar_menu(self):
        """Caesar cipher menu"""
        print(f"\n{Fore.CYAN}[CAESAR] Choose operation:{Style.RESET_ALL}")
        print(f"1. Encrypt with Caesar cipher")
        print(f"2. Decrypt with known shift")
        print(f"3. Brute force (try all shifts)")
        
        choice = self.get_input("Choose (1/2/3)")
        if not choice:
            return
        
        if choice == '1':
            text = self.get_input("Text to encrypt")
            shift = self.get_input("Shift value (1-25)")
            if text and shift:
                try:
                    command = f'python scripts/crypto/caesar_cipher.py "{text}" --encrypt {shift}'
                    subprocess.run(command, shell=True)
                except Exception as e:
                    print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        elif choice == '2':
            text = self.get_input("Text to decrypt")
            shift = self.get_input("Shift value (1-25)")
            if text and shift:
                try:
                    command = f'python scripts/crypto/caesar_cipher.py "{text}" --decrypt {shift}'
                    subprocess.run(command, shell=True)
                except Exception as e:
                    print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        elif choice == '3':
            text = self.get_input("Text to brute force")
            if text:
                try:
                    command = f'python scripts/crypto/caesar_cipher.py "{text}" --brute-force'
                    subprocess.run(command, shell=True)
                except Exception as e:
                    print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def rot13_menu(self):
        """ROT13 menu"""
        text = self.get_input("Text for ROT13 (encode/decode)")
        if text:
            try:
                command = f'python scripts/crypto/caesar_cipher.py "{text}" --decrypt 13'
                subprocess.run(command, shell=True)
            except Exception as e:
                print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def binary_menu(self):
        """Binary encode/decode menu"""
        print(f"\n{Fore.CYAN}[BINARY] Choose operation:{Style.RESET_ALL}")
        print(f"1. Encode to Binary")
        print(f"2. Decode from Binary")
        
        choice = self.get_input("Choose (1/2)")
        if not choice:
            return
        
        if choice == '1':
            text = self.get_input("Text to encode")
            if text:
                binary = ''.join(format(ord(char), '08b') for char in text)
                print(f"\n{Fore.GREEN}[ENCODED] {binary}{Style.RESET_ALL}")
        elif choice == '2':
            text = self.get_input("Binary text to decode")
            if text:
                try:
                    if len(text) % 8 == 0:
                        decoded = ''.join(chr(int(text[i:i+8], 2)) for i in range(0, len(text), 8))
                        print(f"\n{Fore.GREEN}[DECODED] {decoded}{Style.RESET_ALL}")
                    else:
                        print(f"\n{Fore.RED}[ERROR] Binary length must be multiple of 8{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}[ERROR] Invalid binary: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def hash_analysis(self):
        """Hash analysis"""
        text = self.get_input("Enter hash value")
        if not text:
            return
        
        print(f"\n{Fore.BLUE}[HASH ANALYSIS]{Style.RESET_ALL}")
        print(f"Hash: {text}")
        print(f"Length: {len(text)} characters")
        
        # Determine hash type
        if len(text) == 32 and all(c in '0123456789abcdefABCDEF' for c in text):
            hash_type = "MD5"
        elif len(text) == 40 and all(c in '0123456789abcdefABCDEF' for c in text):
            hash_type = "SHA1"
        elif len(text) == 64 and all(c in '0123456789abcdefABCDEF' for c in text):
            hash_type = "SHA256"
        elif len(text) == 128 and all(c in '0123456789abcdefABCDEF' for c in text):
            hash_type = "SHA512"
        else:
            hash_type = "Unknown"
        
        print(f"Detected Type: {hash_type}")
        
        if hash_type != "Unknown":
            print(f"\n{Fore.YELLOW}[INFO] This is a {hash_type} hash{Style.RESET_ALL}")
            print(f"Hashes cannot be directly decrypted, only cracked through:")
            print(f"• Dictionary attacks")
            print(f"• Brute force attacks")
            print(f"• Rainbow tables")
            print(f"\nTry online hash crackers:")
            print(f"• https://crackstation.net/")
            print(f"• https://md5decrypt.net/")
            print(f"• https://hashkiller.io/")
        else:
            print(f"\n{Fore.YELLOW}[WARNING] Hash type not recognized{Style.RESET_ALL}")
            print(f"This might not be a standard hash format")
        
        self.pause()
    
    def comprehensive_analysis(self):
        """Comprehensive analysis using all methods"""
        text = self.get_input("Enter encrypted/encoded text")
        if text:
            try:
                command = f'python scripts/crypto/caesar_cipher.py "{text}" --brute-force'
                subprocess.run(command, shell=True)
            except Exception as e:
                print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def custom_caesar(self):
        """Custom Caesar cipher with specific shift"""
        print(f"\n{Fore.CYAN}[CUSTOM CAESAR] Enter details:{Style.RESET_ALL}")
        text = self.get_input("Text to process")
        shift = self.get_input("Shift value (1-25)")
        operation = self.get_input("Operation (encrypt/decrypt)")
        
        if text and shift and operation:
            try:
                if operation.lower() in ['encrypt', 'e']:
                    command = f'python scripts/crypto/caesar_cipher.py "{text}" --encrypt {shift}'
                elif operation.lower() in ['decrypt', 'd']:
                    command = f'python scripts/crypto/caesar_cipher.py "{text}" --decrypt {shift}'
                else:
                    print(f"{Fore.RED}[ERROR] Invalid operation. Use 'encrypt' or 'decrypt'{Style.RESET_ALL}")
                    self.pause()
                    return
                
                subprocess.run(command, shell=True)
            except Exception as e:
                print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def run(self):
        """Main application loop"""
        while True:
            try:
                self.clear_screen()
                self.print_banner()
                self.print_main_menu()
                
                choice = self.get_input("Choose option (0-10)")
                
                if choice == '0':
                    print(f"\n{Fore.GREEN}Thank you for using the Encryption Selector!{Style.RESET_ALL}")
                    print(f"{Fore.BLUE}Developed by S.Tamilselvan{Style.RESET_ALL}")
                    break
                elif choice in self.methods:
                    method_name, method_func = self.methods[choice]
                    print(f"\n{Fore.BLUE}[SELECTED] {method_name}{Style.RESET_ALL}")
                    method_func()
                else:
                    print(f"{Fore.RED}[ERROR] Invalid option. Please choose 0-10.{Style.RESET_ALL}")
                    self.pause()
                    
            except KeyboardInterrupt:
                print(f"\n\n{Fore.YELLOW}[WARNING] Interrupted by user{Style.RESET_ALL}")
                confirm = input(f"Do you want to exit? (y/n): ").strip().lower()
                if confirm in ['y', 'yes']:
                    break
            except Exception as e:
                print(f"\n{Fore.RED}[ERROR] Unexpected error: {e}{Style.RESET_ALL}")
                self.pause()

def main():
    """Main function"""
    selector = EncryptionSelector()
    selector.run()

if __name__ == '__main__':
    main()

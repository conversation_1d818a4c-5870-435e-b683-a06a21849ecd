#!/usr/bin/env python3
"""
Gemini AI-Powered Encryption Finder Demo
Developed by S.Tamilselvan

Demonstrates the advanced capabilities of the Gemini AI integration
for encryption detection and steganography analysis.
"""

import os
import sys
import subprocess
from colorama import Fore, Style, init

# Initialize colorama
init()

def run_demo(command, description):
    """Run a demo command and display results"""
    print(f"\n{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[GEMINI DEMO] {description}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Command: {command}{Style.RESET_ALL}")
    print()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}{result.stderr}{Style.RESET_ALL}")
    except subprocess.TimeoutExpired:
        print(f"{Fore.RED}[TIMEOUT] Command took too long to execute{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] {e}{Style.RESET_ALL}")
    
    input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")

def create_demo_files():
    """Create demo files for testing"""
    print(f"{Fore.BLUE}[SETUP] Creating demo files for Gemini analysis...{Style.RESET_ALL}")
    
    # Create demo directory
    os.makedirs("gemini_demo_files", exist_ok=True)
    
    # Demo 1: Base64 encoded flag
    base64_content = "ZmxhZ3tnZW1pbmlfYWlfcG93ZXJlZF9ieV9zX3RhbWlsc2VsdmFufQ=="
    with open("gemini_demo_files/base64_demo.txt", "w") as f:
        f.write(f"Hidden message: {base64_content}\n")
        f.write("This file contains a Base64 encoded flag that Gemini AI should detect.\n")
    
    # Demo 2: Caesar cipher
    caesar_content = "synt{pnrfne_pvcure_qrgrpgrq_ol_trzvar_nv}"
    with open("gemini_demo_files/caesar_demo.txt", "w") as f:
        f.write(f"Encrypted text: {caesar_content}\n")
        f.write("This is a ROT13 Caesar cipher that should be detected by AI.\n")
    
    # Demo 3: Hex encoded content
    hex_content = "666c61677b6865785f656e636f64696e675f64657465637465647d"
    with open("gemini_demo_files/hex_demo.txt", "w") as f:
        f.write(f"Hexadecimal data: {hex_content}\n")
        f.write("This contains hex-encoded information.\n")
    
    # Demo 4: Mixed encoding (Base64 of hex)
    mixed_content = "NjY2Yzc0Njc3Yjc0Njg2NTVmNjc2NTZkNjk2ZTY5NWY2MTY5NWY2ZDY5Nzg2NTY0NWY2NTZlNjM2Zjc2NGY3ZA=="
    with open("gemini_demo_files/mixed_demo.txt", "w") as f:
        f.write(f"Complex encoding: {mixed_content}\n")
        f.write("This is Base64 encoded hex data - multi-layer encryption.\n")
    
    # Demo 5: Steganography simulation
    stego_content = """
    This is a normal looking text file.
    Nothing suspicious here at all.
    Just regular content for demonstration.
    
    <!-- Hidden comment: flag{hidden_in_html_comment} -->
    
    More normal text to make it look innocent.
    The flag is hidden in the HTML comment above.
    """
    with open("gemini_demo_files/stego_demo.html", "w") as f:
        f.write(stego_content)
    
    print(f"{Fore.GREEN}[SUCCESS] Demo files created in gemini_demo_files/{Style.RESET_ALL}")

def main():
    """Main demo function"""
    print(f"{Fore.GREEN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}    GEMINI AI-POWERED ENCRYPTION FINDER DEMO{Style.RESET_ALL}")
    print(f"{Fore.GREEN}    Developed by S.Tamilselvan{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*70}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}This demo showcases the advanced Gemini AI integration for:{Style.RESET_ALL}")
    print(f"• Intelligent encryption pattern recognition")
    print(f"• AI-powered cipher identification")
    print(f"• Advanced steganography detection")
    print(f"• Multi-layer decryption analysis")
    print(f"• Confidence-based result ranking")
    
    response = input(f"\n{Fore.YELLOW}Do you want to continue with the demo? (y/n): {Style.RESET_ALL}").lower()
    if response not in ['y', 'yes']:
        print("Demo cancelled.")
        return
    
    # Create demo files
    create_demo_files()
    
    # Demo 1: Simple text analysis with Gemini AI
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "ZmxhZ3tnZW1pbmlfYWlfZGVtb30=" --type text',
        "Gemini AI Analysis - Base64 Encoded Text"
    )
    
    # Demo 2: Caesar cipher detection
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "synt{pnrfne_pvcure_qrgrpgrq}" --type text',
        "Gemini AI Analysis - Caesar Cipher Detection"
    )
    
    # Demo 3: File analysis with AI
    run_demo(
        'python scripts/steganography/all_encryption_finder.py gemini_demo_files/base64_demo.txt --type file',
        "Gemini AI Analysis - File with Base64 Content"
    )
    
    # Demo 4: Complex multi-layer encryption
    run_demo(
        'python scripts/steganography/all_encryption_finder.py gemini_demo_files/mixed_demo.txt --type file',
        "Gemini AI Analysis - Multi-layer Encryption"
    )
    
    # Demo 5: Steganography detection
    run_demo(
        'python scripts/steganography/all_encryption_finder.py gemini_demo_files/stego_demo.html --type file',
        "Gemini AI Analysis - Steganography Detection"
    )
    
    # Demo 6: Hex analysis
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "666c61677b6865785f656e636f64696e677d" --type text',
        "Gemini AI Analysis - Hex Encoded Content"
    )
    
    # Demo 7: URL analysis (if available)
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "https://httpbin.org/base64/ZmxhZ3t1cmxfYW5hbHlzaXN9" --type url',
        "Gemini AI Analysis - URL Content Analysis"
    )
    
    # Demo 8: Show help and features
    run_demo(
        'python scripts/steganography/all_encryption_finder.py --help',
        "Gemini AI Tool - Help and Features"
    )
    
    # Demo 9: Analysis without AI (comparison)
    run_demo(
        'python scripts/steganography/all_encryption_finder.py "ZmxhZ3tnZW1pbmlfYWlfZGVtb30=" --type text --no-ai',
        "Traditional Analysis (No AI) - Comparison"
    )
    
    # Demo 10: Master analyzer with Gemini integration
    run_demo(
        'python ctf_master_analyzer.py gemini_demo_files/mixed_demo.txt --type file',
        "Master Analyzer with Gemini AI Integration"
    )
    
    print(f"\n{Fore.GREEN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}           GEMINI AI DEMO COMPLETE!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*70}{Style.RESET_ALL}")
    
    print(f"\n{Fore.BLUE}What you've learned:{Style.RESET_ALL}")
    print(f"• How Gemini AI enhances encryption detection")
    print(f"• AI-powered pattern recognition capabilities")
    print(f"• Multi-layer decryption with confidence scoring")
    print(f"• Advanced steganography analysis")
    print(f"• Integration with the master analyzer system")
    
    print(f"\n{Fore.YELLOW}Key Features Demonstrated:{Style.RESET_ALL}")
    print(f"• Base64, Hex, Caesar cipher detection")
    print(f"• Multi-layer encoding analysis")
    print(f"• HTML comment steganography")
    print(f"• URL content analysis")
    print(f"• AI vs traditional analysis comparison")
    
    print(f"\n{Fore.CYAN}Advanced Usage Examples:{Style.RESET_ALL}")
    print(f"# Analyze any suspicious file")
    print(f"python scripts/steganography/all_encryption_finder.py suspicious_file.txt")
    print(f"")
    print(f"# Analyze encrypted text with AI")
    print(f'python scripts/steganography/all_encryption_finder.py "encrypted_text_here" --type text')
    print(f"")
    print(f"# Analyze web content")
    print(f"python scripts/steganography/all_encryption_finder.py http://target.com --type url")
    print(f"")
    print(f"# Use custom API key")
    print(f"python scripts/steganography/all_encryption_finder.py file.txt --api-key YOUR_KEY")
    
    print(f"\n{Fore.GREEN}API Information:{Style.RESET_ALL}")
    print(f"• Gemini API Endpoint: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent")
    print(f"• API Key: AIzaSyABge7vHFTpvZykbQd_EDvoT35-eSvZp2s")
    print(f"• Model: gemini-2.0-flash (Latest version)")
    print(f"• Features: Advanced pattern recognition, multi-language support")
    
    print(f"\n{Fore.BLUE}Next Steps:{Style.RESET_ALL}")
    print(f"• Try the tool on real CTF challenges")
    print(f"• Experiment with different file types")
    print(f"• Compare AI vs traditional analysis results")
    print(f"• Use the master analyzer for comprehensive analysis")
    
    # Cleanup option
    cleanup = input(f"\n{Fore.YELLOW}Do you want to clean up demo files? (y/n): {Style.RESET_ALL}").lower()
    if cleanup in ['y', 'yes']:
        import shutil
        try:
            shutil.rmtree("gemini_demo_files")
            print(f"{Fore.GREEN}[CLEANUP] Demo files removed{Style.RESET_ALL}")
        except:
            print(f"{Fore.YELLOW}[WARNING] Could not remove demo files{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}Happy AI-Powered CTF Hunting! 🤖🚀{Style.RESET_ALL}")

if __name__ == "__main__":
    main()

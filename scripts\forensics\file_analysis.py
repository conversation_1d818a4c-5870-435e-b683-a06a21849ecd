#!/usr/bin/env python3
"""
File Analysis Tool for CTF Forensics Challenges
Analyzes files for hidden data, metadata, and suspicious content
"""

import os
import sys
import hashlib
import binascii
import argparse
import magic
from colorama import Fore, Style, init

# Initialize colorama
init()

class FileAnalyzer:
    def __init__(self, filepath):
        self.filepath = filepath
        self.filename = os.path.basename(filepath)
        self.filesize = os.path.getsize(filepath) if os.path.exists(filepath) else 0

    def basic_info(self):
        """Get basic file information"""
        print(f"{Fore.BLUE}[BASIC INFO]{Style.RESET_ALL}")
        print("-" * 40)
        print(f"File: {self.filename}")
        print(f"Path: {self.filepath}")
        print(f"Size: {self.filesize} bytes ({self.filesize / 1024:.2f} KB)")
        
        if os.path.exists(self.filepath):
            # File type detection
            try:
                file_type = magic.from_file(self.filepath)
                print(f"Type: {file_type}")
            except:
                print("Type: Unable to determine")
            
            # File permissions
            stat_info = os.stat(self.filepath)
            print(f"Permissions: {oct(stat_info.st_mode)[-3:]}")
            
            # Timestamps
            import datetime
            mtime = datetime.datetime.fromtimestamp(stat_info.st_mtime)
            print(f"Modified: {mtime}")
        print()

    def calculate_hashes(self):
        """Calculate file hashes"""
        print(f"{Fore.BLUE}[HASH ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        try:
            with open(self.filepath, 'rb') as f:
                content = f.read()
            
            # Calculate various hashes
            md5_hash = hashlib.md5(content).hexdigest()
            sha1_hash = hashlib.sha1(content).hexdigest()
            sha256_hash = hashlib.sha256(content).hexdigest()
            
            print(f"MD5:    {md5_hash}")
            print(f"SHA1:   {sha1_hash}")
            print(f"SHA256: {sha256_hash}")
            
        except Exception as e:
            print(f"{Fore.RED}Error calculating hashes: {e}{Style.RESET_ALL}")
        print()

    def hex_analysis(self, bytes_to_show=256):
        """Analyze file in hexadecimal"""
        print(f"{Fore.BLUE}[HEX ANALYSIS] (First {bytes_to_show} bytes){Style.RESET_ALL}")
        print("-" * 60)
        
        try:
            with open(self.filepath, 'rb') as f:
                data = f.read(bytes_to_show)
            
            # Display hex dump
            for i in range(0, len(data), 16):
                chunk = data[i:i+16]
                hex_part = ' '.join(f'{b:02x}' for b in chunk)
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
                print(f"{i:08x}: {hex_part:<48} |{ascii_part}|")
                
        except Exception as e:
            print(f"{Fore.RED}Error reading file: {e}{Style.RESET_ALL}")
        print()

    def search_strings(self, min_length=4):
        """Extract printable strings from file"""
        print(f"{Fore.BLUE}[STRING ANALYSIS] (Min length: {min_length}){Style.RESET_ALL}")
        print("-" * 50)
        
        try:
            with open(self.filepath, 'rb') as f:
                data = f.read()
            
            strings = []
            current_string = ""
            
            for byte in data:
                if 32 <= byte <= 126:  # Printable ASCII
                    current_string += chr(byte)
                else:
                    if len(current_string) >= min_length:
                        strings.append(current_string)
                    current_string = ""
            
            # Don't forget the last string
            if len(current_string) >= min_length:
                strings.append(current_string)
            
            # Show interesting strings
            ctf_keywords = ['flag', 'ctf', 'password', 'secret', 'key', 'admin', 'user']
            interesting_strings = []
            
            for string in strings:
                string_lower = string.lower()
                if any(keyword in string_lower for keyword in ctf_keywords):
                    interesting_strings.append(string)
            
            if interesting_strings:
                print(f"{Fore.GREEN}[INTERESTING STRINGS]{Style.RESET_ALL}")
                for string in interesting_strings[:10]:  # Show first 10
                    print(f"  {string}")
                print()
            
            # Show all strings (limited)
            print(f"Found {len(strings)} strings. Showing first 20:")
            for string in strings[:20]:
                if len(string) > 80:
                    print(f"  {string[:77]}...")
                else:
                    print(f"  {string}")
                    
        except Exception as e:
            print(f"{Fore.RED}Error extracting strings: {e}{Style.RESET_ALL}")
        print()

    def detect_embedded_files(self):
        """Detect embedded files by looking for file signatures"""
        print(f"{Fore.BLUE}[EMBEDDED FILE DETECTION]{Style.RESET_ALL}")
        print("-" * 40)
        
        # Common file signatures
        signatures = {
            b'\x89PNG\r\n\x1a\n': 'PNG Image',
            b'\xff\xd8\xff': 'JPEG Image',
            b'GIF87a': 'GIF Image (87a)',
            b'GIF89a': 'GIF Image (89a)',
            b'PK\x03\x04': 'ZIP Archive',
            b'PK\x05\x06': 'ZIP Archive (empty)',
            b'Rar!\x1a\x07\x00': 'RAR Archive',
            b'\x7fELF': 'ELF Executable',
            b'MZ': 'PE Executable',
            b'%PDF': 'PDF Document',
            b'\xd0\xcf\x11\xe0': 'Microsoft Office Document',
        }
        
        try:
            with open(self.filepath, 'rb') as f:
                data = f.read()
            
            found_signatures = []
            
            for signature, description in signatures.items():
                offset = data.find(signature)
                if offset != -1:
                    found_signatures.append((offset, description, signature))
            
            if found_signatures:
                print(f"{Fore.GREEN}Found {len(found_signatures)} embedded file signature(s):{Style.RESET_ALL}")
                for offset, description, signature in found_signatures:
                    hex_sig = ' '.join(f'{b:02x}' for b in signature)
                    print(f"  Offset {offset:08x}: {description} ({hex_sig})")
            else:
                print("No embedded file signatures detected.")
                
        except Exception as e:
            print(f"{Fore.RED}Error detecting embedded files: {e}{Style.RESET_ALL}")
        print()

    def entropy_analysis(self):
        """Calculate file entropy to detect encryption/compression"""
        print(f"{Fore.BLUE}[ENTROPY ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        try:
            with open(self.filepath, 'rb') as f:
                data = f.read()
            
            if not data:
                print("File is empty.")
                return
            
            # Calculate byte frequency
            byte_counts = [0] * 256
            for byte in data:
                byte_counts[byte] += 1
            
            # Calculate entropy
            entropy = 0
            data_len = len(data)
            
            for count in byte_counts:
                if count > 0:
                    probability = count / data_len
                    entropy -= probability * (probability.bit_length() - 1)
            
            print(f"Entropy: {entropy:.4f} bits per byte")
            
            # Interpret entropy
            if entropy > 7.5:
                print(f"{Fore.RED}High entropy - likely encrypted or compressed{Style.RESET_ALL}")
            elif entropy > 6.0:
                print(f"{Fore.YELLOW}Medium entropy - possibly compressed or binary{Style.RESET_ALL}")
            else:
                print(f"{Fore.GREEN}Low entropy - likely text or structured data{Style.RESET_ALL}")
                
        except Exception as e:
            print(f"{Fore.RED}Error calculating entropy: {e}{Style.RESET_ALL}")
        print()

    def search_flag_patterns(self):
        """Search for common CTF flag patterns"""
        print(f"{Fore.BLUE}[FLAG PATTERN SEARCH]{Style.RESET_ALL}")
        print("-" * 40)
        
        import re
        
        try:
            with open(self.filepath, 'rb') as f:
                data = f.read()
            
            # Try to decode as text
            try:
                text = data.decode('utf-8', errors='ignore')
            except:
                text = data.decode('latin-1', errors='ignore')
            
            # Common flag patterns
            patterns = [
                (r'flag\{[^}]+\}', 'Standard flag format'),
                (r'ctf\{[^}]+\}', 'CTF flag format'),
                (r'[a-zA-Z0-9_]+\{[^}]+\}', 'Generic flag format'),
                (r'[A-Z0-9]{20,}', 'Long uppercase string'),
                (r'[a-f0-9]{32}', 'MD5 hash'),
                (r'[a-f0-9]{40}', 'SHA1 hash'),
                (r'[a-f0-9]{64}', 'SHA256 hash'),
            ]
            
            found_patterns = []
            
            for pattern, description in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    found_patterns.extend([(match, description) for match in matches])
            
            if found_patterns:
                print(f"{Fore.GREEN}Found {len(found_patterns)} potential flag(s):{Style.RESET_ALL}")
                for match, description in found_patterns:
                    print(f"  {description}: {match}")
            else:
                print("No flag patterns detected.")
                
        except Exception as e:
            print(f"{Fore.RED}Error searching for flags: {e}{Style.RESET_ALL}")
        print()

def main():
    parser = argparse.ArgumentParser(description='CTF File Analysis Tool')
    parser.add_argument('file', help='File to analyze')
    parser.add_argument('--basic', action='store_true', help='Show basic file info only')
    parser.add_argument('--hashes', action='store_true', help='Calculate file hashes')
    parser.add_argument('--hex', type=int, metavar='BYTES', default=256,
                       help='Show hex dump (default: 256 bytes)')
    parser.add_argument('--strings', type=int, metavar='MIN_LEN', default=4,
                       help='Extract strings (default: min length 4)')
    parser.add_argument('--embedded', action='store_true', help='Detect embedded files')
    parser.add_argument('--entropy', action='store_true', help='Calculate entropy')
    parser.add_argument('--flags', action='store_true', help='Search for flag patterns')
    parser.add_argument('--all', action='store_true', help='Run all analyses')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print(f"{Fore.RED}[ERROR] File not found: {args.file}{Style.RESET_ALL}")
        return
    
    analyzer = FileAnalyzer(args.file)
    
    # Run requested analyses
    if args.all or args.basic or not any([args.hashes, args.hex, args.strings, 
                                         args.embedded, args.entropy, args.flags]):
        analyzer.basic_info()
    
    if args.all or args.hashes:
        analyzer.calculate_hashes()
    
    if args.all or args.hex:
        analyzer.hex_analysis(args.hex)
    
    if args.all or args.strings:
        analyzer.search_strings(args.strings)
    
    if args.all or args.embedded:
        analyzer.detect_embedded_files()
    
    if args.all or args.entropy:
        analyzer.entropy_analysis()
    
    if args.all or args.flags:
        analyzer.search_flag_patterns()

if __name__ == '__main__':
    main()

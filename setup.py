#!/usr/bin/env python3
"""
Setup script for CTF Finder
Installs dependencies and sets up the environment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"[INFO] {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"[SUCCESS] {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} failed: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def install_python_dependencies():
    """Install Python dependencies"""
    print("\n=== Installing Python Dependencies ===")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip", 
                "Upgrading pip")
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                    "Installing Python packages")
    else:
        print("[WARNING] requirements.txt not found")

def install_system_tools():
    """Install system tools based on the operating system"""
    print("\n=== Installing System Tools ===")
    
    system = platform.system().lower()
    
    if system == "linux":
        # Detect distribution
        try:
            with open("/etc/os-release") as f:
                os_info = f.read().lower()
            
            if "ubuntu" in os_info or "debian" in os_info:
                install_debian_tools()
            elif "fedora" in os_info or "rhel" in os_info or "centos" in os_info:
                install_fedora_tools()
            elif "arch" in os_info:
                install_arch_tools()
            else:
                print("[INFO] Unknown Linux distribution. Please install tools manually.")
                
        except FileNotFoundError:
            print("[WARNING] Could not detect Linux distribution")
            
    elif system == "darwin":  # macOS
        install_macos_tools()
    elif system == "windows":
        install_windows_tools()
    else:
        print(f"[WARNING] Unsupported operating system: {system}")

def install_debian_tools():
    """Install tools on Debian/Ubuntu"""
    tools = [
        "nmap", "gobuster", "sqlmap", "nikto", "john", "hashcat",
        "steghide", "binwalk", "foremost", "exiftool", "file",
        "hexdump", "strings", "radare2", "gdb", "python3-magic"
    ]
    
    # Update package list
    run_command("sudo apt update", "Updating package list")
    
    # Install tools
    tools_str = " ".join(tools)
    run_command(f"sudo apt install -y {tools_str}", 
                "Installing security tools")

def install_fedora_tools():
    """Install tools on Fedora/RHEL/CentOS"""
    tools = [
        "nmap", "sqlmap", "john", "hashcat", "steghide", 
        "binwalk", "foremost", "exiftool", "file", "radare2", 
        "gdb", "python3-magic"
    ]
    
    tools_str = " ".join(tools)
    run_command(f"sudo dnf install -y {tools_str}", 
                "Installing security tools")

def install_arch_tools():
    """Install tools on Arch Linux"""
    tools = [
        "nmap", "gobuster", "sqlmap", "john", "hashcat",
        "steghide", "binwalk", "foremost", "exiftool", "file",
        "radare2", "gdb", "python-magic"
    ]
    
    tools_str = " ".join(tools)
    run_command(f"sudo pacman -S --noconfirm {tools_str}", 
                "Installing security tools")

def install_macos_tools():
    """Install tools on macOS using Homebrew"""
    # Check if Homebrew is installed
    if not run_command("which brew", "Checking for Homebrew"):
        print("[INFO] Installing Homebrew...")
        run_command('/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
                    "Installing Homebrew")
    
    tools = [
        "nmap", "gobuster", "sqlmap", "john", "hashcat",
        "steghide", "binwalk", "foremost", "exiftool", "file",
        "radare2", "gdb"
    ]
    
    for tool in tools:
        run_command(f"brew install {tool}", f"Installing {tool}")

def install_windows_tools():
    """Install tools on Windows"""
    print("[INFO] For Windows, please manually install the following tools:")
    print("- Nmap: https://nmap.org/download.html")
    print("- John the Ripper: https://www.openwall.com/john/")
    print("- Hashcat: https://hashcat.net/hashcat/")
    print("- Wireshark: https://www.wireshark.org/download.html")
    print("- 7-Zip: https://www.7-zip.org/")
    print("- HxD (Hex Editor): https://mh-nexus.de/en/hxd/")
    print("- Python: https://www.python.org/downloads/")
    print("\nConsider using WSL (Windows Subsystem for Linux) for better tool support.")

def create_directories():
    """Create necessary directories"""
    print("\n=== Creating Directory Structure ===")
    
    directories = [
        "scripts/web", "scripts/crypto", "scripts/forensics",
        "scripts/steganography", "scripts/binary",
        "wordlists", "tools/custom_tools", "tools/third_party",
        "challenges/web", "challenges/crypto", "challenges/forensics",
        "challenges/steganography", "challenges/binary",
        "docs/techniques", "docs/writeups", "docs/references"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"[INFO] Created directory: {directory}")

def make_scripts_executable():
    """Make Python scripts executable on Unix-like systems"""
    if platform.system().lower() != "windows":
        print("\n=== Making Scripts Executable ===")
        
        script_dirs = ["scripts/web", "scripts/crypto", "scripts/forensics",
                      "scripts/steganography", "scripts/binary"]
        
        for script_dir in script_dirs:
            if os.path.exists(script_dir):
                for script_file in os.listdir(script_dir):
                    if script_file.endswith(".py"):
                        script_path = os.path.join(script_dir, script_file)
                        run_command(f"chmod +x {script_path}", 
                                   f"Making {script_path} executable")

def main():
    """Main setup function"""
    print("CTF Finder Setup Script")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("[ERROR] Python 3.6 or higher is required")
        sys.exit(1)
    
    print(f"[INFO] Python version: {sys.version}")
    print(f"[INFO] Operating system: {platform.system()} {platform.release()}")
    
    # Create directories
    create_directories()
    
    # Install Python dependencies
    install_python_dependencies()
    
    # Install system tools
    response = input("\nDo you want to install system tools? (y/n): ").lower()
    if response in ['y', 'yes']:
        install_system_tools()
    
    # Make scripts executable
    make_scripts_executable()
    
    print("\n" + "=" * 40)
    print("[SUCCESS] Setup completed!")
    print("\nNext steps:")
    print("1. Review the README.md file for usage instructions")
    print("2. Test the installation by running: python scripts/crypto/caesar_cipher.py --create-sample")
    print("3. Explore the tools and start solving CTF challenges!")
    print("\nHappy hacking! 🚀")

if __name__ == "__main__":
    main()

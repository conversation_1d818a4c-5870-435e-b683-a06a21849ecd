#!/usr/bin/env python3
"""
Advanced Steganography & Encryption Finder with Gemini AI Integration
Developed by <PERSON><PERSON>

This tool combines traditional cryptanalysis with AI-powered pattern recognition
using Google's Gemini API for enhanced encryption detection and breaking.

Features:
- AI-powered encryption pattern recognition
- Advanced steganography detection
- Multi-layer decryption analysis
- Gemini API integration for intelligent analysis
- CLI mode with comprehensive reporting
"""

import os
import sys
import json
import base64
import binascii
import argparse
import requests
import re
from pathlib import Path
from colorama import Fore, Style, init

# Initialize colorama
init()

class GeminiEncryptionFinder:
    def __init__(self, api_key=None):
        self.api_key = api_key or "AIzaSyABge7vHFTpvZykbQd_EDvoT35-eSvZp2s"
        self.endpoint_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
        self.found_encryptions = []
        self.ai_analysis_results = []
        
        # Comprehensive encryption patterns
        self.encryption_patterns = {
            'base64': r'[A-Za-z0-9+/]{4,}={0,2}',
            'hex': r'[a-fA-F0-9]{8,}',
            'binary': r'[01]{8,}',
            'caesar': r'[A-Za-z]{10,}',
            'morse': r'[.-]{10,}',
            'url_encoded': r'%[0-9a-fA-F]{2}',
            'html_entities': r'&#[0-9]+;',
            'unicode_escape': r'\\u[0-9a-fA-F]{4}',
            'rot13': r'[A-Za-z]{5,}',
            'atbash': r'[A-Za-z]{5,}',
            'vigenere': r'[A-Za-z]{15,}',
            'substitution': r'[A-Za-z]{20,}',
            'md5': r'[a-f0-9]{32}',
            'sha1': r'[a-f0-9]{40}',
            'sha256': r'[a-f0-9]{64}',
            'jwt': r'eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*',
            'pgp': r'-----BEGIN PGP',
            'ssh_key': r'ssh-rsa|ssh-ed25519|ssh-dss',
            'private_key': r'-----BEGIN.*PRIVATE KEY-----',
            'certificate': r'-----BEGIN CERTIFICATE-----'
        }
        
        # Flag patterns for detection
        self.flag_patterns = [
            r'flag\{[^}]+\}', r'ctf\{[^}]+\}', r'FLAG\{[^}]+\}', r'CTF\{[^}]+\}',
            r'[a-zA-Z0-9_]+\{[^}]+\}', r'\{[^}]+\}', r'\[[^\]]+\]'
        ]

    def analyze_with_gemini(self, data, analysis_type="general"):
        """Use Gemini AI for intelligent encryption analysis"""
        print(f"{Fore.CYAN}[AI ANALYSIS] Using Gemini AI for {analysis_type} analysis...{Style.RESET_ALL}")
        
        # Prepare prompt based on analysis type
        prompts = {
            "general": f"""
Analyze this data for hidden encryption, steganography, or encoded content. Look for:
1. Encryption patterns (Caesar, Vigenère, substitution ciphers)
2. Encoding schemes (Base64, Hex, Binary, URL encoding)
3. Steganography indicators
4. Hidden flags in CTF format (flag{{...}}, ctf{{...}})
5. Hash values (MD5, SHA1, SHA256)
6. Any suspicious patterns or anomalies

Data to analyze:
{data[:2000]}...

Provide specific findings with confidence levels and suggested decryption methods.
""",
            "steganography": f"""
Analyze this data for steganography techniques:
1. LSB (Least Significant Bit) patterns
2. Hidden text in metadata
3. Frequency analysis anomalies
4. Suspicious bit patterns
5. Embedded files or data
6. Palette-based hiding techniques

Data: {data[:1500]}...

Identify potential steganography methods and extraction techniques.
""",
            "cryptography": f"""
Perform cryptographic analysis on this data:
1. Cipher identification (Caesar, Vigenère, Playfair, etc.)
2. Key length estimation
3. Frequency analysis results
4. Possible decryption approaches
5. Weakness identification

Encrypted data: {data[:1000]}...

Suggest the most likely cipher type and breaking approach.
"""
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        
        try:
            headers = {
                'Content-Type': 'application/json',
            }
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            url = f"{self.endpoint_url}?key={self.api_key}"
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    ai_response = result['candidates'][0]['content']['parts'][0]['text']
                    self.ai_analysis_results.append({
                        'type': analysis_type,
                        'response': ai_response,
                        'confidence': 'AI-Generated'
                    })
                    
                    print(f"{Fore.GREEN}[AI SUCCESS] Gemini analysis completed{Style.RESET_ALL}")
                    return ai_response
                else:
                    print(f"{Fore.YELLOW}[AI WARNING] No response from Gemini{Style.RESET_ALL}")
                    return None
            else:
                print(f"{Fore.RED}[AI ERROR] Gemini API error: {response.status_code}{Style.RESET_ALL}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"{Fore.RED}[AI ERROR] Failed to connect to Gemini: {e}{Style.RESET_ALL}")
            return None

    def comprehensive_analysis(self, target, target_type='auto'):
        """Perform comprehensive encryption and steganography analysis"""
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}    ADVANCED ENCRYPTION & STEGANOGRAPHY FINDER{Style.RESET_ALL}")
        print(f"{Fore.GREEN}    Powered by Gemini AI - Developed by S.Tamilselvan{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        
        print(f"\n{Fore.BLUE}[INFO] Target: {target}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}[INFO] Analysis Type: {target_type}{Style.RESET_ALL}")
        
        # Determine target type
        if target_type == 'auto':
            target_type = self.detect_target_type(target)
        
        # Load data based on target type
        if target_type == 'file':
            data = self.load_file_data(target)
        elif target_type == 'text':
            data = target
        elif target_type == 'url':
            data = self.load_url_data(target)
        else:
            print(f"{Fore.RED}[ERROR] Unknown target type: {target_type}{Style.RESET_ALL}")
            return
        
        if not data:
            print(f"{Fore.RED}[ERROR] Failed to load data{Style.RESET_ALL}")
            return
        
        print(f"{Fore.BLUE}[INFO] Data loaded: {len(data)} characters{Style.RESET_ALL}")
        
        # Phase 1: Traditional pattern analysis
        print(f"\n{Fore.YELLOW}[PHASE 1] Traditional Pattern Analysis{Style.RESET_ALL}")
        self.traditional_analysis(data)
        
        # Phase 2: AI-powered analysis
        print(f"\n{Fore.YELLOW}[PHASE 2] AI-Powered Analysis with Gemini{Style.RESET_ALL}")
        self.ai_powered_analysis(data)
        
        # Phase 3: Advanced decryption attempts
        print(f"\n{Fore.YELLOW}[PHASE 3] Advanced Decryption Attempts{Style.RESET_ALL}")
        self.advanced_decryption(data)
        
        # Phase 4: Steganography detection
        if target_type == 'file':
            print(f"\n{Fore.YELLOW}[PHASE 4] Steganography Detection{Style.RESET_ALL}")
            self.steganography_analysis(target)
        
        # Generate comprehensive report
        self.generate_comprehensive_report()

    def detect_target_type(self, target):
        """Detect target type automatically"""
        if target.startswith(('http://', 'https://')):
            return 'url'
        elif os.path.isfile(target):
            return 'file'
        else:
            return 'text'

    def load_file_data(self, filepath):
        """Load data from file with multiple encoding attempts"""
        try:
            # Try binary first
            with open(filepath, 'rb') as f:
                binary_data = f.read()
            
            # Try different text encodings
            encodings = ['utf-8', 'ascii', 'latin-1', 'utf-16', 'utf-32']
            for encoding in encodings:
                try:
                    text_data = binary_data.decode(encoding, errors='ignore')
                    if len(text_data.strip()) > 0:
                        return text_data
                except:
                    continue
            
            # If text decoding fails, return hex representation
            return binary_data.hex()
            
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Failed to load file: {e}{Style.RESET_ALL}")
            return None

    def load_url_data(self, url):
        """Load data from URL"""
        try:
            response = requests.get(url, timeout=10)
            return response.text
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Failed to load URL: {e}{Style.RESET_ALL}")
            return None

    def traditional_analysis(self, data):
        """Traditional pattern-based analysis"""
        print(f"{Fore.CYAN}[TRADITIONAL] Pattern Recognition Analysis{Style.RESET_ALL}")
        
        found_patterns = {}
        
        # Check for each encryption pattern
        for pattern_name, pattern_regex in self.encryption_patterns.items():
            matches = re.findall(pattern_regex, data, re.IGNORECASE | re.MULTILINE)
            if matches:
                found_patterns[pattern_name] = matches[:5]  # Limit to first 5 matches
                print(f"{Fore.GREEN}[FOUND] {pattern_name}: {len(matches)} matches{Style.RESET_ALL}")
                
                # Try to decode immediately
                self.attempt_decoding(matches[0], pattern_name)
        
        # Check for flag patterns
        for pattern in self.flag_patterns:
            matches = re.findall(pattern, data, re.IGNORECASE)
            if matches:
                for match in matches:
                    self.found_encryptions.append({
                        'type': 'flag_direct',
                        'content': match,
                        'confidence': 95,
                        'method': 'Direct Pattern Match'
                    })
                    print(f"{Fore.GREEN}[FLAG FOUND] {match}{Style.RESET_ALL}")

    def attempt_decoding(self, data, encoding_type):
        """Attempt to decode data based on detected pattern"""
        try:
            decoded = None
            
            if encoding_type == 'base64':
                decoded = base64.b64decode(data).decode('utf-8', errors='ignore')
            elif encoding_type == 'hex':
                decoded = bytes.fromhex(data).decode('utf-8', errors='ignore')
            elif encoding_type == 'binary':
                if len(data) % 8 == 0:
                    decoded = ''.join(chr(int(data[i:i+8], 2)) for i in range(0, len(data), 8))
            elif encoding_type == 'url_encoded':
                import urllib.parse
                decoded = urllib.parse.unquote(data)
            elif encoding_type == 'caesar':
                # Try all Caesar shifts
                for shift in range(1, 26):
                    caesar_decoded = self.caesar_decode(data, shift)
                    if any(pattern in caesar_decoded.lower() for pattern in ['flag', 'ctf']):
                        decoded = caesar_decoded
                        break
            
            if decoded and len(decoded.strip()) > 0:
                print(f"{Fore.CYAN}[DECODED] {encoding_type}: {decoded[:100]}...{Style.RESET_ALL}")
                
                # Check if decoded content contains flags
                for pattern in self.flag_patterns:
                    matches = re.findall(pattern, decoded, re.IGNORECASE)
                    for match in matches:
                        self.found_encryptions.append({
                            'type': encoding_type,
                            'content': match,
                            'confidence': 90,
                            'method': f'{encoding_type.title()} Decoding'
                        })
                
                # Recursively analyze decoded content
                if len(decoded) > 20:
                    self.traditional_analysis(decoded)
        
        except Exception as e:
            pass  # Ignore decoding errors

    def caesar_decode(self, text, shift):
        """Decode Caesar cipher with given shift"""
        result = ""
        for char in text:
            if char.isalpha():
                ascii_offset = 65 if char.isupper() else 97
                result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
            else:
                result += char
        return result

    def ai_powered_analysis(self, data):
        """AI-powered analysis using Gemini"""
        # General analysis
        general_analysis = self.analyze_with_gemini(data, "general")
        if general_analysis:
            self.extract_ai_findings(general_analysis, "general")
        
        # Cryptography-specific analysis
        crypto_analysis = self.analyze_with_gemini(data, "cryptography")
        if crypto_analysis:
            self.extract_ai_findings(crypto_analysis, "cryptography")
        
        # Steganography analysis if data looks like it could contain hidden content
        if len(data) > 1000 or any(char in data for char in ['\x00', '\xff']):
            stego_analysis = self.analyze_with_gemini(data, "steganography")
            if stego_analysis:
                self.extract_ai_findings(stego_analysis, "steganography")

    def extract_ai_findings(self, ai_response, analysis_type):
        """Extract actionable findings from AI response"""
        # Look for confidence indicators in AI response
        confidence_keywords = {
            'high': ['definitely', 'certainly', 'clearly', 'obviously'],
            'medium': ['likely', 'probably', 'appears', 'seems'],
            'low': ['possibly', 'might', 'could', 'maybe']
        }
        
        confidence = 70  # Default confidence
        for level, keywords in confidence_keywords.items():
            if any(keyword in ai_response.lower() for keyword in keywords):
                if level == 'high':
                    confidence = 90
                elif level == 'medium':
                    confidence = 75
                else:
                    confidence = 60
                break
        
        # Extract potential flags or decoded content from AI response
        for pattern in self.flag_patterns:
            matches = re.findall(pattern, ai_response, re.IGNORECASE)
            for match in matches:
                self.found_encryptions.append({
                    'type': f'ai_{analysis_type}',
                    'content': match,
                    'confidence': confidence,
                    'method': f'Gemini AI {analysis_type.title()} Analysis'
                })

    def advanced_decryption(self, data):
        """Advanced decryption attempts"""
        print(f"{Fore.CYAN}[ADVANCED] Multi-layer Decryption{Style.RESET_ALL}")
        
        # Try common cipher combinations
        cipher_combinations = [
            ['base64', 'caesar'],
            ['hex', 'rot13'],
            ['url_encoded', 'base64'],
            ['base64', 'hex'],
            ['caesar', 'base64']
        ]
        
        for combination in cipher_combinations:
            try:
                current_data = data
                for cipher in combination:
                    current_data = self.apply_cipher_decode(current_data, cipher)
                    if not current_data:
                        break
                
                if current_data and current_data != data:
                    print(f"{Fore.CYAN}[COMBO] {' -> '.join(combination)}: {current_data[:100]}...{Style.RESET_ALL}")
                    
                    # Check for flags in combined decoding
                    for pattern in self.flag_patterns:
                        matches = re.findall(pattern, current_data, re.IGNORECASE)
                        for match in matches:
                            self.found_encryptions.append({
                                'type': 'combination',
                                'content': match,
                                'confidence': 85,
                                'method': f"Combined: {' -> '.join(combination)}"
                            })
            except:
                continue

    def apply_cipher_decode(self, data, cipher_type):
        """Apply specific cipher decoding"""
        try:
            if cipher_type == 'base64':
                return base64.b64decode(data).decode('utf-8', errors='ignore')
            elif cipher_type == 'hex':
                return bytes.fromhex(data).decode('utf-8', errors='ignore')
            elif cipher_type == 'caesar':
                # Try ROT13 as default
                return self.caesar_decode(data, 13)
            elif cipher_type == 'rot13':
                return self.caesar_decode(data, 13)
            elif cipher_type == 'url_encoded':
                import urllib.parse
                return urllib.parse.unquote(data)
            else:
                return data
        except:
            return None

    def steganography_analysis(self, filepath):
        """Advanced steganography analysis for files"""
        print(f"{Fore.CYAN}[STEGANOGRAPHY] File-based Analysis{Style.RESET_ALL}")
        
        try:
            file_ext = Path(filepath).suffix.lower()
            
            if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                self.analyze_image_steganography(filepath)
            elif file_ext in ['.wav', '.mp3', '.flac']:
                self.analyze_audio_steganography(filepath)
            else:
                self.analyze_generic_steganography(filepath)
        
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Steganography analysis failed: {e}{Style.RESET_ALL}")

    def analyze_image_steganography(self, filepath):
        """Analyze image files for steganography"""
        try:
            with open(filepath, 'rb') as f:
                image_data = f.read()
            
            # Look for data after image end markers
            end_markers = [b'\xff\xd9', b'IEND', b'\x00\x3b']
            
            for marker in end_markers:
                marker_pos = image_data.find(marker)
                if marker_pos != -1:
                    trailing_data = image_data[marker_pos + len(marker):]
                    if len(trailing_data) > 10:
                        print(f"{Fore.YELLOW}[SUSPICIOUS] {len(trailing_data)} bytes after image end{Style.RESET_ALL}")
                        
                        # Analyze trailing data
                        trailing_text = trailing_data.decode('utf-8', errors='ignore')
                        self.traditional_analysis(trailing_text)
        
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Image steganography analysis failed: {e}{Style.RESET_ALL}")

    def analyze_audio_steganography(self, filepath):
        """Analyze audio files for steganography"""
        print(f"{Fore.YELLOW}[AUDIO] Basic steganography check{Style.RESET_ALL}")
        # Basic file analysis - would need specialized libraries for full audio analysis
        self.analyze_generic_steganography(filepath)

    def analyze_generic_steganography(self, filepath):
        """Generic steganography analysis"""
        try:
            with open(filepath, 'rb') as f:
                file_data = f.read()
            
            # Convert to text and analyze
            file_text = file_data.decode('utf-8', errors='ignore')
            self.traditional_analysis(file_text)
            
            # Analyze hex representation
            hex_data = file_data.hex()
            self.traditional_analysis(hex_data)
        
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Generic steganography analysis failed: {e}{Style.RESET_ALL}")

    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report"""
        print(f"\n{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}    COMPREHENSIVE ENCRYPTION & STEGANOGRAPHY REPORT{Style.RESET_ALL}")
        print(f"{Fore.GREEN}    Powered by Gemini AI - Developed by S.Tamilselvan{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        
        # Remove duplicates and sort by confidence
        unique_findings = {}
        for finding in self.found_encryptions:
            content = finding['content']
            if content not in unique_findings or finding['confidence'] > unique_findings[content]['confidence']:
                unique_findings[content] = finding
        
        sorted_findings = sorted(unique_findings.values(), key=lambda x: x['confidence'], reverse=True)
        
        print(f"\n{Fore.CYAN}[SUMMARY]{Style.RESET_ALL}")
        print(f"Total unique findings: {len(sorted_findings)}")
        print(f"AI analysis results: {len(self.ai_analysis_results)}")
        
        if sorted_findings:
            print(f"\n{Fore.GREEN}[ENCRYPTION/STEGANOGRAPHY FINDINGS]{Style.RESET_ALL}")
            
            for i, finding in enumerate(sorted_findings, 1):
                confidence_color = Fore.GREEN if finding['confidence'] >= 85 else Fore.YELLOW if finding['confidence'] >= 70 else Fore.RED
                
                print(f"\n{Fore.BLUE}[{i:2d}] {confidence_color}Confidence: {finding['confidence']}%{Style.RESET_ALL}")
                print(f"     Type: {finding['type']}")
                print(f"     Method: {finding['method']}")
                print(f"     Content: {finding['content']}")
        
        # Display AI analysis results
        if self.ai_analysis_results:
            print(f"\n{Fore.CYAN}[AI ANALYSIS RESULTS]{Style.RESET_ALL}")
            for i, ai_result in enumerate(self.ai_analysis_results, 1):
                print(f"\n{Fore.BLUE}[AI-{i}] {ai_result['type'].title()} Analysis:{Style.RESET_ALL}")
                print(f"{ai_result['response'][:500]}...")
        
        if not sorted_findings:
            print(f"\n{Fore.YELLOW}[RESULT] No encryption or steganography detected{Style.RESET_ALL}")
            print("Consider:")
            print("- Manual analysis with specialized tools")
            print("- Different file formats or encoding methods")
            print("- Password-protected steganography")
            print("- Advanced cryptographic techniques")
        
        print(f"\n{Fore.BLUE}[ANALYSIS COMPLETE] Gemini AI-Powered Analysis by S.Tamilselvan{Style.RESET_ALL}")

def main():
    parser = argparse.ArgumentParser(description='Advanced Encryption & Steganography Finder with Gemini AI')
    parser.add_argument('target', help='Target to analyze (file, URL, or text)')
    parser.add_argument('--type', choices=['auto', 'file', 'text', 'url'], 
                       default='auto', help='Target type (default: auto-detect)')
    parser.add_argument('--api-key', help='Gemini API key (optional)')
    parser.add_argument('--no-ai', action='store_true', help='Disable AI analysis')
    
    args = parser.parse_args()
    
    # Initialize analyzer
    api_key = args.api_key if args.api_key else "AIzaSyABge7vHFTpvZykbQd_EDvoT35-eSvZp2s"
    analyzer = GeminiEncryptionFinder(api_key)
    
    # Disable AI if requested
    if args.no_ai:
        analyzer.analyze_with_gemini = lambda *args, **kwargs: None
        print(f"{Fore.YELLOW}[INFO] AI analysis disabled{Style.RESET_ALL}")
    
    # Perform analysis
    analyzer.comprehensive_analysis(args.target, args.type)

if __name__ == '__main__':
    main()

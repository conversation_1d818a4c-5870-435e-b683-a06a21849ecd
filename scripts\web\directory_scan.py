#!/usr/bin/env python3
"""
Directory Scanner for CTF Web Challenges
Performs directory and file enumeration on web applications
"""

import requests
import threading
import argparse
from urllib.parse import urljoin
from colorama import Fore, Style, init

# Initialize colorama for cross-platform colored output
init()

class DirectoryScanner:
    def __init__(self, target_url, wordlist_file, threads=10, extensions=None):
        self.target_url = target_url.rstrip('/')
        self.wordlist_file = wordlist_file
        self.threads = threads
        self.extensions = extensions or ['', '.php', '.html', '.txt', '.js', '.css']
        self.found_paths = []
        self.session = requests.Session()
        
        # Set headers to appear more legitimate
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def load_wordlist(self):
        """Load wordlist from file"""
        try:
            with open(self.wordlist_file, 'r', encoding='utf-8', errors='ignore') as f:
                return [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            print(f"{Fore.RED}[ERROR] Wordlist file not found: {self.wordlist_file}{Style.RESET_ALL}")
            return []

    def test_path(self, path):
        """Test a single path for existence"""
        for ext in self.extensions:
            test_path = path + ext
            url = urljoin(self.target_url + '/', test_path)
            
            try:
                response = self.session.get(url, timeout=5, allow_redirects=False)
                
                # Check for interesting status codes
                if response.status_code in [200, 301, 302, 403]:
                    size = len(response.content)
                    status_color = Fore.GREEN if response.status_code == 200 else Fore.YELLOW
                    
                    result = {
                        'url': url,
                        'status': response.status_code,
                        'size': size,
                        'path': test_path
                    }
                    
                    self.found_paths.append(result)
                    print(f"{status_color}[{response.status_code}] {url} ({size} bytes){Style.RESET_ALL}")
                    
                    # Check for common CTF indicators in response
                    if any(keyword in response.text.lower() for keyword in ['flag', 'ctf', 'password', 'admin']):
                        print(f"{Fore.CYAN}[INTERESTING] Potential CTF content found in {url}{Style.RESET_ALL}")
                        
            except requests.exceptions.RequestException:
                pass  # Ignore connection errors

    def scan(self):
        """Perform the directory scan"""
        wordlist = self.load_wordlist()
        if not wordlist:
            return
            
        print(f"{Fore.BLUE}[INFO] Starting directory scan on {self.target_url}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}[INFO] Wordlist: {self.wordlist_file} ({len(wordlist)} entries){Style.RESET_ALL}")
        print(f"{Fore.BLUE}[INFO] Extensions: {', '.join(self.extensions)}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}[INFO] Threads: {self.threads}{Style.RESET_ALL}")
        print("-" * 60)
        
        # Create and start threads
        threads = []
        wordlist_chunks = [wordlist[i::self.threads] for i in range(self.threads)]
        
        for chunk in wordlist_chunks:
            thread = threading.Thread(target=self.scan_chunk, args=(chunk,))
            thread.daemon = True
            thread.start()
            threads.append(thread)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
            
        print("-" * 60)
        print(f"{Fore.GREEN}[COMPLETE] Found {len(self.found_paths)} interesting paths{Style.RESET_ALL}")
        
        # Sort results by status code
        self.found_paths.sort(key=lambda x: x['status'])
        return self.found_paths

    def scan_chunk(self, chunk):
        """Scan a chunk of the wordlist"""
        for word in chunk:
            self.test_path(word)

def create_sample_wordlist():
    """Create a sample wordlist for testing"""
    common_paths = [
        'admin', 'login', 'index', 'home', 'about', 'contact',
        'config', 'backup', 'test', 'debug', 'api', 'upload',
        'files', 'images', 'css', 'js', 'robots.txt', 'sitemap.xml',
        'flag', 'secret', 'hidden', 'private', 'internal',
        'phpinfo', 'info', 'status', 'health', 'version'
    ]
    
    with open('wordlists/common_directories.txt', 'w') as f:
        for path in common_paths:
            f.write(path + '\n')
    
    print(f"{Fore.GREEN}[INFO] Created sample wordlist: wordlists/common_directories.txt{Style.RESET_ALL}")

def main():
    parser = argparse.ArgumentParser(description='CTF Directory Scanner')
    parser.add_argument('url', help='Target URL (e.g., http://example.com)')
    parser.add_argument('-w', '--wordlist', default='wordlists/common_directories.txt',
                       help='Wordlist file path')
    parser.add_argument('-t', '--threads', type=int, default=10,
                       help='Number of threads (default: 10)')
    parser.add_argument('-e', '--extensions', nargs='+',
                       default=['', '.php', '.html', '.txt', '.js'],
                       help='File extensions to test')
    parser.add_argument('--create-wordlist', action='store_true',
                       help='Create a sample wordlist')
    
    args = parser.parse_args()
    
    if args.create_wordlist:
        create_sample_wordlist()
        return
    
    scanner = DirectoryScanner(args.url, args.wordlist, args.threads, args.extensions)
    results = scanner.scan()
    
    # Save results to file
    if results:
        with open('scan_results.txt', 'w') as f:
            for result in results:
                f.write(f"{result['status']} {result['url']} ({result['size']} bytes)\n")
        print(f"{Fore.GREEN}[INFO] Results saved to scan_results.txt{Style.RESET_ALL}")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
CTF Finder Quick Launcher
Developed by S.<PERSON>selvan

Quick launcher script that automatically starts the CTF Finder terminal menu
with relief conditions and user-friendly interface.
"""

import os
import sys
import subprocess
from pathlib import Path
from colorama import Fore, Style, init

# Initialize colorama
init()

def check_requirements():
    """Check if required files and dependencies exist"""
    print(f"{Fore.BLUE}🔍 Checking CTF Finder requirements...{Style.RESET_ALL}")
    
    # Check required files
    required_files = [
        'ctf_terminal_menu.py',
        'ctf_master_analyzer.py',
        'scripts/steganography/all_encryption_finder.py',
        'scripts/advanced_flag_detector.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"{Fore.RED}❌ Missing required files:{Style.RESET_ALL}")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    print(f"{Fore.GREEN}✅ All required files found{Style.RESET_ALL}")
    return True

def check_python_version():
    """Check Python version compatibility"""
    print(f"{Fore.BLUE}🐍 Checking Python version...{Style.RESET_ALL}")
    
    if sys.version_info < (3, 6):
        print(f"{Fore.RED}❌ Python 3.6+ required. Current: {sys.version}{Style.RESET_ALL}")
        return False
    
    print(f"{Fore.GREEN}✅ Python version: {sys.version.split()[0]}{Style.RESET_ALL}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print(f"\n{Fore.YELLOW}📦 Installing dependencies...{Style.RESET_ALL}")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        print(f"{Fore.GREEN}✅ Dependencies installed successfully{Style.RESET_ALL}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"{Fore.YELLOW}⚠️  Some dependencies may not be available{Style.RESET_ALL}")
        print(f"{Fore.BLUE}ℹ️  The tool will still work with basic functionality{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to install dependencies: {e}{Style.RESET_ALL}")
        return False

def show_welcome_banner():
    """Show welcome banner"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════════════════════╗
║                          🎯 CTF FINDER LAUNCHER 🎯                          ║
║                         100% Accuracy Analysis System                       ║
║                          Developed by S.Tamilselvan                         ║
╚══════════════════════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.GREEN}🚀 Welcome to the most advanced CTF analysis system!{Style.RESET_ALL}

{Fore.BLUE}Features:{Style.RESET_ALL}
• 🤖 Gemini AI-powered analysis
• 🎯 100% accuracy flag detection
• 🖥️  User-friendly terminal interface
• 🔧 Comprehensive tool collection
• 📊 Analysis history tracking
• ⚡ Relief conditions and error handling

{Fore.YELLOW}Getting started is easy - just follow the prompts!{Style.RESET_ALL}
"""
    print(banner)

def show_quick_help():
    """Show quick help information"""
    help_text = f"""
{Fore.CYAN}📚 Quick Help:{Style.RESET_ALL}

{Fore.GREEN}For Beginners:{Style.RESET_ALL}
• Use the interactive menu (option 1) - it guides you through everything
• Start with the Master Analyzer for comprehensive analysis
• Try the demos to learn how the tools work

{Fore.YELLOW}For Advanced Users:{Style.RESET_ALL}
• Use command-line tools directly for specific tasks
• Combine multiple tools for complex analysis
• Customize analysis parameters for specific needs

{Fore.BLUE}Common Tasks:{Style.RESET_ALL}
• Analyze suspicious files: Use File Analysis menu
• Crack ciphers: Use Cryptography Analysis menu
• Find hidden data in images: Use Steganography menu
• Scan websites: Use Web Analysis menu
• AI-powered analysis: Use Gemini AI menu

{Fore.RED}Need Help?{Style.RESET_ALL}
• Check the README.md file for detailed documentation
• Use the demo modes to see examples
• All tools have built-in help (--help option)
"""
    print(help_text)

def main():
    """Main launcher function"""
    try:
        show_welcome_banner()
        
        # Check system requirements
        if not check_python_version():
            input(f"\n{Fore.RED}Press Enter to exit...{Style.RESET_ALL}")
            return
        
        if not check_requirements():
            print(f"\n{Fore.YELLOW}⚠️  Some files are missing. Please ensure you have the complete CTF Finder package.{Style.RESET_ALL}")
            choice = input(f"Continue anyway? (y/n): ").strip().lower()
            if choice not in ['y', 'yes']:
                return
        
        # Offer to install dependencies
        print(f"\n{Fore.CYAN}🔧 Setup Options:{Style.RESET_ALL}")
        print(f"1. 🚀 Launch CTF Finder (recommended)")
        print(f"2. 📦 Install/Update dependencies first")
        print(f"3. 📚 Show quick help")
        print(f"4. 🚪 Exit")
        
        while True:
            choice = input(f"\n{Fore.CYAN}Choose option (1-4): {Style.RESET_ALL}").strip()
            
            if choice == '1':
                # Launch CTF Finder
                print(f"\n{Fore.GREEN}🚀 Launching CTF Finder Interactive Menu...{Style.RESET_ALL}")
                print(f"{Fore.BLUE}Tip: Use Ctrl+C to return to menu at any time{Style.RESET_ALL}")
                
                try:
                    subprocess.run([sys.executable, 'ctf_terminal_menu.py'])
                except KeyboardInterrupt:
                    print(f"\n{Fore.YELLOW}⚠️  Returned to launcher{Style.RESET_ALL}")
                except FileNotFoundError:
                    print(f"\n{Fore.RED}❌ ctf_terminal_menu.py not found{Style.RESET_ALL}")
                    print(f"Please ensure you have the complete CTF Finder package")
                except Exception as e:
                    print(f"\n{Fore.RED}❌ Error launching CTF Finder: {e}{Style.RESET_ALL}")
                
                # Ask if user wants to continue
                continue_choice = input(f"\n{Fore.CYAN}Return to launcher menu? (y/n): {Style.RESET_ALL}").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
                
            elif choice == '2':
                # Install dependencies
                if install_dependencies():
                    print(f"\n{Fore.GREEN}✅ Setup complete! You can now launch CTF Finder.{Style.RESET_ALL}")
                else:
                    print(f"\n{Fore.YELLOW}⚠️  Setup had some issues, but you can still try to launch CTF Finder.{Style.RESET_ALL}")
                
            elif choice == '3':
                # Show help
                show_quick_help()
                input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")
                
            elif choice == '4':
                # Exit
                print(f"\n{Fore.GREEN}Thank you for using CTF Finder!{Style.RESET_ALL}")
                print(f"{Fore.BLUE}Developed by S.Tamilselvan{Style.RESET_ALL}")
                break
                
            else:
                print(f"{Fore.RED}❌ Invalid option. Please choose 1-4.{Style.RESET_ALL}")
    
    except KeyboardInterrupt:
        print(f"\n\n{Fore.YELLOW}⚠️  Launcher interrupted by user{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Thank you for using CTF Finder!{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Launcher error: {e}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}You can try running 'python ctf_terminal_menu.py' directly{Style.RESET_ALL}")

if __name__ == '__main__':
    main()

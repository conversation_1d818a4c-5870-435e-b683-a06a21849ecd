#!/usr/bin/env python3
"""
CTF Finder - Interactive Terminal Menu System
Developed by S.Tamilselvan

User-friendly terminal interface with menu options for all CTF analysis functions.
Provides relief conditions and easy navigation for all tools.
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from colorama import Fore, Style, init

# Initialize colorama
init()

class CTFTerminalMenu:
    def __init__(self):
        self.current_directory = os.getcwd()
        self.last_target = ""
        self.analysis_history = []
        
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_banner(self):
        """Print application banner"""
        banner = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════════════════════════════════╗
║                    CTF FINDER - INTERACTIVE TERMINAL MENU                                ║
║                         100% Accuracy Analysis System                                    ║
║                          Developed by S.Tamilselvan                                      ║
║                                                                                          ║
╚══════════════════════════════════════════════════════════════════════════════════════════           ╝{Style.RESET_ALL}

{Fore.GREEN}Advanced CTF Analysis Tools with AI Integration{Style.RESET_ALL}
{Fore.BLUE}Current Directory: {self.current_directory}{Style.RESET_ALL}
{Fore.YELLOW}Last Target: {self.last_target or 'None'}{Style.RESET_ALL}
"""
        print(banner)
    
    def print_main_menu(self):
        """Print main menu options"""
        menu = f"""
{Fore.CYAN}┌─ MAIN MENU ─────────────────────────────────────────────────────────────────┐{Style.RESET_ALL}
{Fore.GREEN}│  1. Master Analyzer (100% Accuracy - Recommended)                        │{Style.RESET_ALL}
{Fore.GREEN}│  2. Gemini AI-Powered Analysis (Advanced)                                │{Style.RESET_ALL}
{Fore.YELLOW}│  3. Web Application Analysis                                              │{Style.RESET_ALL}
{Fore.YELLOW}│  4. Cryptography Analysis                                                 │{Style.RESET_ALL}
{Fore.YELLOW}│  5. Steganography Analysis                                                │{Style.RESET_ALL}
{Fore.YELLOW}│  6. Forensics Analysis                                                    │{Style.RESET_ALL}
{Fore.YELLOW}│  7. File Analysis                                                         │{Style.RESET_ALL}
{Fore.BLUE}│  8. Tools & Utilities                                                    │{Style.RESET_ALL}
{Fore.BLUE}│  9. Analysis History                                                      │{Style.RESET_ALL}
{Fore.BLUE}│ 10. Demo & Examples                                                       │{Style.RESET_ALL}
{Fore.BLUE}│ 11. Settings & Configuration                                              │{Style.RESET_ALL}
{Fore.RED}│  0. Exit                                                                  │{Style.RESET_ALL}
{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}
"""
        print(menu)
    
    def get_user_input(self, prompt="Enter your choice", valid_options=None):
        """Get user input with validation"""
        while True:
            try:
                choice = input(f"\n{Fore.CYAN}{prompt}: {Style.RESET_ALL}").strip()
                
                if valid_options and choice not in valid_options:
                    print(f"{Fore.RED}[ERROR] Invalid option. Please choose from: {', '.join(valid_options)}{Style.RESET_ALL}")
                    continue

                return choice
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}[WARNING] Operation cancelled by user{Style.RESET_ALL}")
                return None
            except EOFError:
                print(f"\n{Fore.RED}[ERROR] Input error{Style.RESET_ALL}")
                return None
    
    def get_target_input(self, target_type="target"):
        """Get target input from user"""
        print(f"\n{Fore.CYAN}Enter {target_type}:{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Examples:{Style.RESET_ALL}")
        print(f"  - File: /path/to/file.txt or file.jpg")
        print(f"  - URL: http://example.com")
        print(f"  - Text: 'encrypted_text_here'")
        print(f"  - Directory: /path/to/directory")
        
        target = input(f"\n{Fore.CYAN}Target: {Style.RESET_ALL}").strip()
        
        if target:
            self.last_target = target
        
        return target
    
    def run_command_with_feedback(self, command, description):
        """Run command with user feedback and error handling"""
        print(f"\n{Fore.BLUE}[RUNNING] {description}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Command: {command}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{'─' * 80}{Style.RESET_ALL}")
        
        try:
            # Add command to history
            self.analysis_history.append({
                'command': command,
                'description': description,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            })
            
            # Run command
            result = subprocess.run(command, shell=True, capture_output=False, text=True)
            
            print(f"\n{Fore.YELLOW}{'─' * 80}{Style.RESET_ALL}")
            
            if result.returncode == 0:
                print(f"{Fore.GREEN}[SUCCESS] Analysis completed successfully{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}[ERROR] Analysis completed with errors (Exit code: {result.returncode}){Style.RESET_ALL}")

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}[WARNING] Analysis interrupted by user{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}[ERROR] Error running analysis: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def pause(self):
        """Pause and wait for user input"""
        input(f"\n{Fore.GREEN}Press Enter to continue...{Style.RESET_ALL}")
    
    def master_analyzer_menu(self):
        """Master analyzer submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ MASTER ANALYZER (100% Accuracy) ──────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Analyze Target (Auto-detect type)                                      │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Analyze File                                                            │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  3. Analyze Text/String                                                    │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  4. Analyze URL/Website                                                     │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  5. Analyze Directory (Recursive)                                          │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. Advanced Options                                                       │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6'])
            
            if choice == '0':
                break
            elif choice == '1':
                target = self.get_target_input()
                if target:
                    command = f"python ctf_master_analyzer.py \"{target}\""
                    self.run_command_with_feedback(command, "Master Analyzer - Auto Detection")
            elif choice == '2':
                target = self.get_target_input("file path")
                if target:
                    command = f"python ctf_master_analyzer.py \"{target}\" --type file"
                    self.run_command_with_feedback(command, "Master Analyzer - File Analysis")
            elif choice == '3':
                target = self.get_target_input("text/string")
                if target:
                    command = f"python ctf_master_analyzer.py \"{target}\" --type text"
                    self.run_command_with_feedback(command, "Master Analyzer - Text Analysis")
            elif choice == '4':
                target = self.get_target_input("URL")
                if target:
                    command = f"python ctf_master_analyzer.py \"{target}\" --type url"
                    self.run_command_with_feedback(command, "Master Analyzer - URL Analysis")
            elif choice == '5':
                target = self.get_target_input("directory path")
                if target:
                    command = f"python ctf_master_analyzer.py \"{target}\" --type directory"
                    self.run_command_with_feedback(command, "Master Analyzer - Directory Analysis")
            elif choice == '6':
                self.master_analyzer_advanced_menu()
    
    def master_analyzer_advanced_menu(self):
        """Advanced options for master analyzer"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ MASTER ANALYZER - ADVANCED OPTIONS ───────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Advanced configuration options:{Style.RESET_ALL}")
        
        target = self.get_target_input()
        if not target:
            return
        
        print(f"\n{Fore.CYAN}Additional Options:{Style.RESET_ALL}")
        confidence = input(f"Confidence threshold (70-100, default 70): ").strip() or "70"
        output_file = input(f"Output file (optional): ").strip()
        
        command = f"python ctf_master_analyzer.py \"{target}\" --confidence {confidence}"
        if output_file:
            command += f" --output \"{output_file}\""
        
        self.run_command_with_feedback(command, "Master Analyzer - Advanced Configuration")
    
    def gemini_ai_menu(self):
        """Gemini AI analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ GEMINI AI-POWERED ANALYSIS ───────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. AI Analysis (Auto-detect)                                              │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. AI File Analysis                                                       │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  3. AI Text Analysis                                                       │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  4. AI URL Analysis                                                        │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. Run Gemini Demo                                                        │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. AI Configuration                                                       │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  7. Traditional Mode (No AI)                                               │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6', '7'])
            
            if choice == '0':
                break
            elif choice == '1':
                target = self.get_target_input()
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\""
                    self.run_command_with_feedback(command, "Gemini AI - Auto Analysis")
            elif choice == '2':
                target = self.get_target_input("file path")
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\" --type file"
                    self.run_command_with_feedback(command, "Gemini AI - File Analysis")
            elif choice == '3':
                target = self.get_target_input("text/string")
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\" --type text"
                    self.run_command_with_feedback(command, "Gemini AI - Text Analysis")
            elif choice == '4':
                target = self.get_target_input("URL")
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\" --type url"
                    self.run_command_with_feedback(command, "Gemini AI - URL Analysis")
            elif choice == '5':
                command = "python gemini_demo.py"
                self.run_command_with_feedback(command, "Gemini AI - Interactive Demo")
            elif choice == '6':
                self.gemini_config_menu()
            elif choice == '7':
                target = self.get_target_input()
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\" --no-ai"
                    self.run_command_with_feedback(command, "Traditional Analysis (No AI)")
    
    def gemini_config_menu(self):
        """Gemini AI configuration menu"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ GEMINI AI CONFIGURATION ───────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Current Configuration:{Style.RESET_ALL}")
        print(f"API Key: AIzaSyABge7vHFTpvZykbQd_EDvoT35-eSvZp2s")
        print(f"Endpoint: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent")
        print(f"Model: gemini-2.0-flash")
        
        print(f"\n{Fore.CYAN}Options:{Style.RESET_ALL}")
        print(f"1. Use default API key")
        print(f"2. Enter custom API key")
        print(f"3. Test API connection")
        
        choice = self.get_user_input("Choose option", ['1', '2', '3'])
        
        if choice == '2':
            api_key = input(f"\n{Fore.CYAN}Enter your Gemini API key: {Style.RESET_ALL}").strip()
            if api_key:
                target = self.get_target_input()
                if target:
                    command = f"python scripts/steganography/all_encryption_finder.py \"{target}\" --api-key \"{api_key}\""
                    self.run_command_with_feedback(command, "Gemini AI - Custom API Key")
        elif choice == '3':
            command = "python scripts/steganography/all_encryption_finder.py \"test\" --type text"
            self.run_command_with_feedback(command, "Gemini AI - API Connection Test")
        
        self.pause()
    
    def web_analysis_menu(self):
        """Web analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ WEB APPLICATION ANALYSIS ──────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Advanced Web Scanner (Comprehensive)                                   │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Directory Scanner                                                       │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  3. SQL Injection Testing                                                  │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. XSS Vulnerability Scan                                                 │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. Create Wordlist                                                        │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5'])
            
            if choice == '0':
                break
            elif choice == '1':
                url = self.get_target_input("URL (e.g., http://example.com)")
                if url:
                    threads = input(f"Number of threads (default 20): ").strip() or "20"
                    command = f"python scripts/web/advanced_web_scanner.py \"{url}\" --threads {threads}"
                    self.run_command_with_feedback(command, "Advanced Web Scanner")
            elif choice == '2':
                url = self.get_target_input("URL")
                if url:
                    command = f"python scripts/web/directory_scan.py \"{url}\""
                    self.run_command_with_feedback(command, "Directory Scanner")
            elif choice == '3':
                print(f"\n{Fore.YELLOW}SQL Injection testing integrated in Advanced Web Scanner{Style.RESET_ALL}")
                self.pause()
            elif choice == '4':
                print(f"\n{Fore.YELLOW}XSS testing integrated in Advanced Web Scanner{Style.RESET_ALL}")
                self.pause()
            elif choice == '5':
                command = "python scripts/web/directory_scan.py --create-wordlist"
                self.run_command_with_feedback(command, "Create Directory Wordlist")
    
    def crypto_analysis_menu(self):
        """Cryptography analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ CRYPTOGRAPHY ANALYSIS ─────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Caesar Cipher Analysis                                                  │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Brute Force Analysis                                                   │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  3. Frequency Analysis                                                     │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. Encrypt Text                                                           │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  5. Decrypt Text                                                           │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. Create Sample Cipher                                                   │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6'])
            
            if choice == '0':
                break
            elif choice == '1':
                text = self.get_target_input("encrypted text")
                if text:
                    command = f"python scripts/crypto/caesar_cipher.py \"{text}\""
                    self.run_command_with_feedback(command, "Caesar Cipher Analysis")
            elif choice == '2':
                text = self.get_target_input("encrypted text")
                if text:
                    command = f"python scripts/crypto/caesar_cipher.py \"{text}\" --brute-force"
                    self.run_command_with_feedback(command, "Brute Force Analysis")
            elif choice == '3':
                text = self.get_target_input("text for analysis")
                if text:
                    command = f"python scripts/crypto/caesar_cipher.py \"{text}\" --frequency"
                    self.run_command_with_feedback(command, "Frequency Analysis")
            elif choice == '4':
                text = input(f"Text to encrypt: ").strip()
                shift = input(f"Shift value (1-25): ").strip()
                if text and shift:
                    command = f"python scripts/crypto/caesar_cipher.py \"{text}\" --encrypt {shift}"
                    self.run_command_with_feedback(command, "Text Encryption")
            elif choice == '5':
                text = input(f"Text to decrypt: ").strip()
                shift = input(f"Shift value (1-25): ").strip()
                if text and shift:
                    command = f"python scripts/crypto/caesar_cipher.py \"{text}\" --decrypt {shift}"
                    self.run_command_with_feedback(command, "Text Decryption")
            elif choice == '6':
                command = "python scripts/crypto/caesar_cipher.py --create-sample"
                self.run_command_with_feedback(command, "Create Sample Cipher")
    
    def steganography_menu(self):
        """Steganography analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ STEGANOGRAPHY ANALYSIS ────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Advanced Image Analysis                                                 │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. LSB Analysis                                                            │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  3. Metadata Extraction                                                    │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. Color Plane Analysis                                                   │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  5. Statistical Analysis                                                   │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. All Analysis Methods                                                   │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6'])
            
            if choice == '0':
                break
            elif choice == '1':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/advanced_stego_analyzer.py \"{image_path}\""
                    self.run_command_with_feedback(command, "Advanced Image Analysis")
            elif choice == '2':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/image_stego.py \"{image_path}\" --lsb"
                    self.run_command_with_feedback(command, "LSB Analysis")
            elif choice == '3':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/image_stego.py \"{image_path}\" --metadata"
                    self.run_command_with_feedback(command, "Metadata Extraction")
            elif choice == '4':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/image_stego.py \"{image_path}\" --planes"
                    self.run_command_with_feedback(command, "Color Plane Analysis")
            elif choice == '5':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/image_stego.py \"{image_path}\" --histogram"
                    self.run_command_with_feedback(command, "Statistical Analysis")
            elif choice == '6':
                image_path = self.get_target_input("image file path")
                if image_path:
                    command = f"python scripts/steganography/image_stego.py \"{image_path}\" --all"
                    self.run_command_with_feedback(command, "Complete Steganography Analysis")
    
    def forensics_menu(self):
        """Forensics analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ FORENSICS ANALYSIS ────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Complete File Analysis                                                  │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Basic File Info                                                        │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  3. Hash Calculation                                                       │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. String Extraction                                                      │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  5. Embedded File Detection                                                │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. Entropy Analysis                                                       │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  7. Flag Pattern Search                                                    │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6', '7'])
            
            if choice == '0':
                break
            elif choice == '1':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --all"
                    self.run_command_with_feedback(command, "Complete File Analysis")
            elif choice == '2':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --basic"
                    self.run_command_with_feedback(command, "Basic File Information")
            elif choice == '3':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --hashes"
                    self.run_command_with_feedback(command, "Hash Calculation")
            elif choice == '4':
                file_path = self.get_target_input("file path")
                min_length = input(f"Minimum string length (default 4): ").strip() or "4"
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --strings {min_length}"
                    self.run_command_with_feedback(command, "String Extraction")
            elif choice == '5':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --embedded"
                    self.run_command_with_feedback(command, "Embedded File Detection")
            elif choice == '6':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --entropy"
                    self.run_command_with_feedback(command, "Entropy Analysis")
            elif choice == '7':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --flags"
                    self.run_command_with_feedback(command, "Flag Pattern Search")
    
    def file_analysis_menu(self):
        """File analysis submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ FILE ANALYSIS ─────────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Advanced Flag Detection                                                 │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. File Type Detection                                                     │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  3. Hex Dump Analysis                                                      │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. Text Content Analysis                                                  │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. Binary Analysis                                                        │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5'])
            
            if choice == '0':
                break
            elif choice == '1':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/advanced_flag_detector.py \"{file_path}\""
                    self.run_command_with_feedback(command, "Advanced Flag Detection")
            elif choice == '2':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"file \"{file_path}\""
                    self.run_command_with_feedback(command, "File Type Detection")
            elif choice == '3':
                file_path = self.get_target_input("file path")
                bytes_count = input(f"Number of bytes to show (default 256): ").strip() or "256"
                if file_path:
                    command = f"python scripts/forensics/file_analysis.py \"{file_path}\" --hex {bytes_count}"
                    self.run_command_with_feedback(command, "Hex Dump Analysis")
            elif choice == '4':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"python scripts/advanced_flag_detector.py \"{file_path}\" --text"
                    self.run_command_with_feedback(command, "Text Content Analysis")
            elif choice == '5':
                file_path = self.get_target_input("file path")
                if file_path:
                    command = f"strings \"{file_path}\" | head -50"
                    self.run_command_with_feedback(command, "Binary String Analysis")
    
    def tools_utilities_menu(self):
        """Tools and utilities submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ TOOLS & UTILITIES ─────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Setup & Installation                                                   │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Create Wordlists                                                       │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  3. Base64 Encode/Decode                                                   │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. Hex Encode/Decode                                                      │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. File Operations                                                        │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  6. Network Tools                                                          │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6'])
            
            if choice == '0':
                break
            elif choice == '1':
                command = "python setup.py"
                self.run_command_with_feedback(command, "Setup & Installation")
            elif choice == '2':
                self.wordlist_creation_menu()
            elif choice == '3':
                self.base64_tools_menu()
            elif choice == '4':
                self.hex_tools_menu()
            elif choice == '5':
                self.file_operations_menu()
            elif choice == '6':
                self.network_tools_menu()
    
    def wordlist_creation_menu(self):
        """Wordlist creation submenu"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ WORDLIST CREATION ─────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. Directory wordlist                                                     │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Password wordlist                                                      │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  3. Subdomain wordlist                                                     │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        choice = self.get_user_input("Choose wordlist type", ['1', '2', '3'])
        
        if choice == '1':
            command = "python scripts/web/directory_scan.py --create-wordlist"
            self.run_command_with_feedback(command, "Create Directory Wordlist")
        elif choice == '2':
            print(f"\n{Fore.GREEN}Password wordlist already available at: wordlists/common_passwords.txt{Style.RESET_ALL}")
            self.pause()
        elif choice == '3':
            print(f"\n{Fore.YELLOW}Subdomain wordlist creation feature coming soon{Style.RESET_ALL}")
            self.pause()
    
    def base64_tools_menu(self):
        """Base64 encode/decode tools"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ BASE64 TOOLS ──────────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. Encode text to Base64                                                  │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Decode Base64 to text                                                  │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        choice = self.get_user_input("Choose operation", ['1', '2'])
        
        if choice == '1':
            text = input(f"Text to encode: ").strip()
            if text:
                import base64
                encoded = base64.b64encode(text.encode()).decode()
                print(f"\n{Fore.GREEN}Encoded: {encoded}{Style.RESET_ALL}")
        elif choice == '2':
            encoded_text = input(f"Base64 to decode: ").strip()
            if encoded_text:
                try:
                    import base64
                    decoded = base64.b64decode(encoded_text).decode()
                    print(f"\n{Fore.GREEN}Decoded: {decoded}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}Error: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def hex_tools_menu(self):
        """Hex encode/decode tools"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ HEX TOOLS ─────────────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. Encode text to Hex                                                     │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Decode Hex to text                                                     │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        choice = self.get_user_input("Choose operation", ['1', '2'])
        
        if choice == '1':
            text = input(f"Text to encode: ").strip()
            if text:
                encoded = text.encode().hex()
                print(f"\n{Fore.GREEN}Encoded: {encoded}{Style.RESET_ALL}")
        elif choice == '2':
            hex_text = input(f"Hex to decode: ").strip()
            if hex_text:
                try:
                    decoded = bytes.fromhex(hex_text).decode()
                    print(f"\n{Fore.GREEN}Decoded: {decoded}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}Error: {e}{Style.RESET_ALL}")
        
        self.pause()
    
    def file_operations_menu(self):
        """File operations submenu"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ FILE OPERATIONS ───────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. List directory contents                                                 │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Change directory                                                       │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  3. View file content                                                      │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  4. Find files                                                             │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        choice = self.get_user_input("Choose operation", ['1', '2', '3', '4'])
        
        if choice == '1':
            command = "ls -la" if os.name != 'nt' else "dir"
            self.run_command_with_feedback(command, "List Directory Contents")
        elif choice == '2':
            new_dir = input(f"Enter directory path: ").strip()
            if new_dir and os.path.exists(new_dir):
                os.chdir(new_dir)
                self.current_directory = os.getcwd()
                print(f"\n{Fore.GREEN}Changed to: {self.current_directory}{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}Directory not found{Style.RESET_ALL}")
            self.pause()
        elif choice == '3':
            file_path = input(f"Enter file path: ").strip()
            if file_path:
                command = f"cat \"{file_path}\"" if os.name != 'nt' else f"type \"{file_path}\""
                self.run_command_with_feedback(command, "View File Content")
        elif choice == '4':
            pattern = input(f"Enter search pattern: ").strip()
            if pattern:
                command = f"find . -name \"*{pattern}*\"" if os.name != 'nt' else f"dir /s \"*{pattern}*\""
                self.run_command_with_feedback(command, "Find Files")
    
    def network_tools_menu(self):
        """Network tools submenu"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ NETWORK TOOLS ─────────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  1. Ping host                                                              │{Style.RESET_ALL}")
        print(f"{Fore.GREEN}│  2. Nmap scan                                                              │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  3. DNS lookup                                                             │{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}│  4. Traceroute                                                             │{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        choice = self.get_user_input("Choose tool", ['1', '2', '3', '4'])
        
        if choice == '1':
            host = input(f"Enter host to ping: ").strip()
            if host:
                command = f"ping -c 4 {host}" if os.name != 'nt' else f"ping {host}"
                self.run_command_with_feedback(command, "Ping Host")
        elif choice == '2':
            host = input(f"Enter host to scan: ").strip()
            if host:
                command = f"nmap -sV {host}"
                self.run_command_with_feedback(command, "Nmap Scan")
        elif choice == '3':
            domain = input(f"Enter domain for DNS lookup: ").strip()
            if domain:
                command = f"nslookup {domain}"
                self.run_command_with_feedback(command, "DNS Lookup")
        elif choice == '4':
            host = input(f"Enter host for traceroute: ").strip()
            if host:
                command = f"traceroute {host}" if os.name != 'nt' else f"tracert {host}"
                self.run_command_with_feedback(command, "Traceroute")
    
    def analysis_history_menu(self):
        """Analysis history submenu"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ ANALYSIS HISTORY ──────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        
        if not self.analysis_history:
            print(f"{Fore.YELLOW}No analysis history available{Style.RESET_ALL}")
        else:
            print(f"{Fore.GREEN}Recent Analysis Commands:{Style.RESET_ALL}")
            for i, entry in enumerate(self.analysis_history[-10:], 1):  # Show last 10
                print(f"{Fore.BLUE}{i:2d}. {entry['timestamp']} - {entry['description']}{Style.RESET_ALL}")
                print(f"    Command: {entry['command']}")
        
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        
        if self.analysis_history:
            choice = input(f"\nEnter number to re-run command (or Enter to continue): ").strip()
            if choice.isdigit() and 1 <= int(choice) <= len(self.analysis_history[-10:]):
                entry = self.analysis_history[-10:][int(choice) - 1]
                self.run_command_with_feedback(entry['command'], f"Re-run: {entry['description']}")
        else:
            self.pause()
    
    def demo_examples_menu(self):
        """Demo and examples submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ DEMO & EXAMPLES ───────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Main Demo (All Tools)                                                  │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Gemini AI Demo                                                         │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  3. Cryptography Examples                                                  │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  4. Steganography Examples                                                 │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. Web Analysis Examples                                                  │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose demo", ['0', '1', '2', '3', '4', '5'])
            
            if choice == '0':
                break
            elif choice == '1':
                command = "python demo.py"
                self.run_command_with_feedback(command, "Main Demo - All Tools")
            elif choice == '2':
                command = "python gemini_demo.py"
                self.run_command_with_feedback(command, "Gemini AI Demo")
            elif choice == '3':
                command = "python scripts/crypto/caesar_cipher.py --create-sample"
                self.run_command_with_feedback(command, "Create Cryptography Example")
            elif choice == '4':
                print(f"\n{Fore.YELLOW}Steganography examples require image files{Style.RESET_ALL}")
                print(f"Please provide an image file for analysis")
                self.pause()
            elif choice == '5':
                command = "python scripts/web/directory_scan.py --create-wordlist"
                self.run_command_with_feedback(command, "Web Analysis Example")
    
    def settings_menu(self):
        """Settings and configuration submenu"""
        while True:
            self.clear_screen()
            self.print_banner()
            
            print(f"\n{Fore.CYAN}┌─ SETTINGS & CONFIGURATION ──────────────────────────────────────────────────┐{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  1. Change Working Directory                                                │{Style.RESET_ALL}")
            print(f"{Fore.GREEN}│  2. Run Setup                                                              │{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}│  3. Gemini AI Configuration                                                │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  4. View System Information                                                │{Style.RESET_ALL}")
            print(f"{Fore.BLUE}│  5. Clear Analysis History                                                 │{Style.RESET_ALL}")
            print(f"{Fore.RED}│  0. Back to Main Menu                                                      │{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
            
            choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5'])
            
            if choice == '0':
                break
            elif choice == '1':
                new_dir = input(f"Enter new working directory: ").strip()
                if new_dir and os.path.exists(new_dir):
                    os.chdir(new_dir)
                    self.current_directory = os.getcwd()
                    print(f"\n{Fore.GREEN}Working directory changed to: {self.current_directory}{Style.RESET_ALL}")
                else:
                    print(f"\n{Fore.RED}Directory not found{Style.RESET_ALL}")
                self.pause()
            elif choice == '2':
                command = "python setup.py"
                self.run_command_with_feedback(command, "Setup and Installation")
            elif choice == '3':
                self.gemini_config_menu()
            elif choice == '4':
                self.show_system_info()
            elif choice == '5':
                self.analysis_history.clear()
                print(f"\n{Fore.GREEN}Analysis history cleared{Style.RESET_ALL}")
                self.pause()
    
    def show_system_info(self):
        """Show system information"""
        self.clear_screen()
        self.print_banner()
        
        print(f"\n{Fore.CYAN}┌─ SYSTEM INFORMATION ────────────────────────────────────────────────────────┐{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Python Version: {sys.version}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Operating System: {os.name}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Current Directory: {self.current_directory}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Analysis History: {len(self.analysis_history)} entries{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Last Target: {self.last_target or 'None'}{Style.RESET_ALL}")
        
        # Check if required files exist
        required_files = [
            'ctf_master_analyzer.py',
            'scripts/steganography/all_encryption_finder.py',
            'scripts/advanced_flag_detector.py'
        ]
        
        print(f"\n{Fore.YELLOW}Tool Availability:{Style.RESET_ALL}")
        for file in required_files:
            status = "[OK]" if os.path.exists(file) else "[MISSING]"
            print(f"{status} {file}")
        
        print(f"{Fore.CYAN}└─────────────────────────────────────────────────────────────────────────────┘{Style.RESET_ALL}")
        self.pause()
    
    def run(self):
        """Main application loop"""
        while True:
            try:
                self.clear_screen()
                self.print_banner()
                self.print_main_menu()
                
                choice = self.get_user_input("Choose option", ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'])
                
                if choice is None:  # Handle Ctrl+C or EOF
                    break
                elif choice == '0':
                    print(f"\n{Fore.GREEN}Thank you for using CTF Finder!{Style.RESET_ALL}")
                    print(f"{Fore.BLUE}Developed by S.Tamilselvan{Style.RESET_ALL}")
                    break
                elif choice == '1':
                    self.master_analyzer_menu()
                elif choice == '2':
                    self.gemini_ai_menu()
                elif choice == '3':
                    self.web_analysis_menu()
                elif choice == '4':
                    self.crypto_analysis_menu()
                elif choice == '5':
                    self.steganography_menu()
                elif choice == '6':
                    self.forensics_menu()
                elif choice == '7':
                    self.file_analysis_menu()
                elif choice == '8':
                    self.tools_utilities_menu()
                elif choice == '9':
                    self.analysis_history_menu()
                elif choice == '10':
                    self.demo_examples_menu()
                elif choice == '11':
                    self.settings_menu()
                
            except KeyboardInterrupt:
                print(f"\n\n{Fore.YELLOW}[WARNING] Interrupted by user{Style.RESET_ALL}")
                confirm = input(f"Do you want to exit? (y/n): ").strip().lower()
                if confirm in ['y', 'yes']:
                    break
            except Exception as e:
                print(f"\n{Fore.RED}[ERROR] Unexpected error: {e}{Style.RESET_ALL}")
                self.pause()

def main():
    """Main function"""
    try:
        menu = CTFTerminalMenu()
        menu.run()
    except Exception as e:
        print(f"{Fore.RED}Fatal error: {e}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Image Steganography Analysis Tool for CTF Challenges
Analyzes images for hidden data using various techniques
"""

import os
import sys
import argparse
import numpy as np
from PIL import Image, ExifTags
from colorama import Fore, Style, init

# Initialize colorama
init()

class ImageStegoAnalyzer:
    def __init__(self, image_path):
        self.image_path = image_path
        self.image = None
        self.load_image()

    def load_image(self):
        """Load the image file"""
        try:
            self.image = Image.open(self.image_path)
            print(f"{Fore.GREEN}[INFO] Loaded image: {os.path.basename(self.image_path)}{Style.RESET_ALL}")
            print(f"Size: {self.image.size[0]}x{self.image.size[1]}")
            print(f"Mode: {self.image.mode}")
            print(f"Format: {self.image.format}")
            print()
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Failed to load image: {e}{Style.RESET_ALL}")
            sys.exit(1)

    def extract_metadata(self):
        """Extract EXIF and other metadata"""
        print(f"{Fore.BLUE}[METADATA ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        # EXIF data
        try:
            exif_data = self.image._getexif()
            if exif_data:
                print("EXIF Data:")
                for tag_id, value in exif_data.items():
                    tag = ExifTags.TAGS.get(tag_id, tag_id)
                    print(f"  {tag}: {value}")
            else:
                print("No EXIF data found.")
        except:
            print("No EXIF data found.")
        
        # Other metadata
        if hasattr(self.image, 'info') and self.image.info:
            print("\nOther metadata:")
            for key, value in self.image.info.items():
                print(f"  {key}: {value}")
        
        print()

    def lsb_analysis(self):
        """Analyze Least Significant Bits"""
        print(f"{Fore.BLUE}[LSB ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        if self.image.mode not in ['RGB', 'RGBA']:
            print("Converting image to RGB for LSB analysis...")
            rgb_image = self.image.convert('RGB')
        else:
            rgb_image = self.image
        
        # Convert to numpy array
        img_array = np.array(rgb_image)
        height, width = img_array.shape[:2]
        
        # Extract LSBs from each channel
        channels = ['Red', 'Green', 'Blue']
        for i, channel in enumerate(channels):
            print(f"\n{channel} channel LSB extraction:")
            
            # Extract LSBs
            lsb_bits = img_array[:, :, i] & 1
            
            # Convert to binary string
            binary_string = ''
            for row in lsb_bits:
                for bit in row:
                    binary_string += str(bit)
            
            # Try to convert to ASCII
            ascii_text = self.binary_to_ascii(binary_string)
            if ascii_text:
                print(f"  Potential text (first 200 chars): {ascii_text[:200]}")
                
                # Look for flag patterns
                if 'flag' in ascii_text.lower() or 'ctf' in ascii_text.lower():
                    print(f"{Fore.GREEN}  [POTENTIAL FLAG FOUND]{Style.RESET_ALL}")
            else:
                print("  No readable ASCII text found in LSBs")

    def binary_to_ascii(self, binary_string):
        """Convert binary string to ASCII text"""
        try:
            # Pad to multiple of 8
            while len(binary_string) % 8 != 0:
                binary_string += '0'
            
            ascii_text = ''
            for i in range(0, len(binary_string), 8):
                byte = binary_string[i:i+8]
                char_code = int(byte, 2)
                if 32 <= char_code <= 126:  # Printable ASCII
                    ascii_text += chr(char_code)
                elif char_code == 0:  # Null terminator
                    break
                else:
                    ascii_text += '.'
            
            # Return only if we have a reasonable amount of printable text
            printable_ratio = sum(1 for c in ascii_text if c.isprintable()) / len(ascii_text) if ascii_text else 0
            return ascii_text if printable_ratio > 0.7 else None
            
        except:
            return None

    def color_plane_analysis(self):
        """Analyze individual color planes"""
        print(f"{Fore.BLUE}[COLOR PLANE ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        if self.image.mode not in ['RGB', 'RGBA']:
            print("Converting image to RGB for color plane analysis...")
            rgb_image = self.image.convert('RGB')
        else:
            rgb_image = self.image
        
        # Split into color channels
        channels = rgb_image.split()
        channel_names = ['Red', 'Green', 'Blue']
        
        for i, (channel, name) in enumerate(zip(channels, channel_names)):
            print(f"\n{name} channel analysis:")
            
            # Convert to numpy array
            channel_array = np.array(channel)
            
            # Calculate statistics
            mean_val = np.mean(channel_array)
            std_val = np.std(channel_array)
            min_val = np.min(channel_array)
            max_val = np.max(channel_array)
            
            print(f"  Mean: {mean_val:.2f}")
            print(f"  Std Dev: {std_val:.2f}")
            print(f"  Range: {min_val} - {max_val}")
            
            # Check for unusual patterns
            if std_val < 10:
                print(f"  {Fore.YELLOW}[SUSPICIOUS] Very low standard deviation{Style.RESET_ALL}")
            
            # Save individual channel for manual inspection
            output_path = f"channel_{name.lower()}_{os.path.basename(self.image_path)}"
            channel.save(output_path)
            print(f"  Saved channel to: {output_path}")

    def pixel_difference_analysis(self):
        """Analyze pixel differences to detect hidden data"""
        print(f"{Fore.BLUE}[PIXEL DIFFERENCE ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        if self.image.mode not in ['RGB', 'RGBA']:
            rgb_image = self.image.convert('RGB')
        else:
            rgb_image = self.image
        
        img_array = np.array(rgb_image)
        
        # Calculate differences between adjacent pixels
        diff_horizontal = np.diff(img_array, axis=1)
        diff_vertical = np.diff(img_array, axis=0)
        
        # Analyze difference statistics
        h_mean = np.mean(np.abs(diff_horizontal))
        v_mean = np.mean(np.abs(diff_vertical))
        
        print(f"Average horizontal difference: {h_mean:.2f}")
        print(f"Average vertical difference: {v_mean:.2f}")
        
        # Look for patterns in differences
        if h_mean < 1.0 or v_mean < 1.0:
            print(f"{Fore.YELLOW}[SUSPICIOUS] Very low pixel differences detected{Style.RESET_ALL}")
        
        print()

    def histogram_analysis(self):
        """Analyze color histograms for anomalies"""
        print(f"{Fore.BLUE}[HISTOGRAM ANALYSIS]{Style.RESET_ALL}")
        print("-" * 40)
        
        if self.image.mode not in ['RGB', 'RGBA']:
            rgb_image = self.image.convert('RGB')
        else:
            rgb_image = self.image
        
        # Get histograms for each channel
        channels = rgb_image.split()
        channel_names = ['Red', 'Green', 'Blue']
        
        for channel, name in zip(channels, channel_names):
            histogram = channel.histogram()
            
            # Find peaks and anomalies
            max_count = max(histogram)
            max_index = histogram.index(max_count)
            
            # Count zero values
            zero_count = histogram.count(0)
            
            print(f"{name} channel:")
            print(f"  Peak value: {max_index} (count: {max_count})")
            print(f"  Unused values: {zero_count}/256")
            
            # Check for suspicious patterns
            if zero_count > 200:
                print(f"  {Fore.YELLOW}[SUSPICIOUS] Many unused color values{Style.RESET_ALL}")
        
        print()

    def search_embedded_text(self):
        """Search for embedded text in various encodings"""
        print(f"{Fore.BLUE}[EMBEDDED TEXT SEARCH]{Style.RESET_ALL}")
        print("-" * 40)
        
        # Convert image to bytes
        img_bytes = self.image.tobytes()
        
        # Search for text patterns
        try:
            # Try different encodings
            encodings = ['utf-8', 'ascii', 'latin-1']
            
            for encoding in encodings:
                try:
                    text = img_bytes.decode(encoding, errors='ignore')
                    
                    # Look for flag patterns
                    import re
                    flag_patterns = [
                        r'flag\{[^}]+\}',
                        r'ctf\{[^}]+\}',
                        r'[a-zA-Z0-9_]+\{[^}]+\}'
                    ]
                    
                    for pattern in flag_patterns:
                        matches = re.findall(pattern, text, re.IGNORECASE)
                        if matches:
                            print(f"{Fore.GREEN}[FLAG FOUND] {encoding}: {matches[0]}{Style.RESET_ALL}")
                    
                    # Look for readable strings
                    words = re.findall(r'[a-zA-Z]{4,}', text)
                    if len(words) > 10:
                        print(f"Readable words in {encoding}: {' '.join(words[:10])}...")
                        
                except:
                    continue
                    
        except Exception as e:
            print(f"Error searching for embedded text: {e}")
        
        print()

def main():
    parser = argparse.ArgumentParser(description='Image Steganography Analysis Tool')
    parser.add_argument('image', help='Image file to analyze')
    parser.add_argument('--metadata', action='store_true', help='Extract metadata only')
    parser.add_argument('--lsb', action='store_true', help='Perform LSB analysis')
    parser.add_argument('--planes', action='store_true', help='Analyze color planes')
    parser.add_argument('--diff', action='store_true', help='Pixel difference analysis')
    parser.add_argument('--histogram', action='store_true', help='Histogram analysis')
    parser.add_argument('--text', action='store_true', help='Search for embedded text')
    parser.add_argument('--all', action='store_true', help='Run all analyses')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"{Fore.RED}[ERROR] Image file not found: {args.image}{Style.RESET_ALL}")
        return
    
    analyzer = ImageStegoAnalyzer(args.image)
    
    # Run requested analyses
    if args.all or not any([args.metadata, args.lsb, args.planes, args.diff, args.histogram, args.text]):
        analyzer.extract_metadata()
        analyzer.lsb_analysis()
        analyzer.color_plane_analysis()
        analyzer.pixel_difference_analysis()
        analyzer.histogram_analysis()
        analyzer.search_embedded_text()
    else:
        if args.metadata:
            analyzer.extract_metadata()
        if args.lsb:
            analyzer.lsb_analysis()
        if args.planes:
            analyzer.color_plane_analysis()
        if args.diff:
            analyzer.pixel_difference_analysis()
        if args.histogram:
            analyzer.histogram_analysis()
        if args.text:
            analyzer.search_embedded_text()

if __name__ == '__main__':
    main()
